package com.fxiaoke.open.erpsyncdata.dbproxy.dao


import com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao.CHInterfaceMonitorDao
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil
import com.google.common.collect.Lists
import org.apache.commons.lang3.ObjectUtils
import org.apache.ibatis.cursor.Cursor
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import org.springframework.transaction.annotation.Transactional
import spock.lang.Ignore
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2023/4/26 20:11:16
 */
// TODO: Ignore Spring
@Ignore
@ContextConfiguration(["classpath:spring-test.xml"])
class AdminSyncPloyDetailDaoTest extends Specification {

    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private CHInterfaceMonitorDao chInterfaceMonitorDao;

    static {
        System.setProperty("process.profile", "fstest");
    }

    def "findObjFuncUsagePloy"() {
        expect:
        def ploy1 = adminSyncPloyDetailDao.findCrmMasterObjFuncUsagePloy("AccountObj", null, null, 0, 100)
        def ploy2 = adminSyncPloyDetailDao.findErpMasterObjFuncUsagePloy("BD_Customer", null, null, 0, 100)
        def ploy3 = adminSyncPloyDetailDao.findCrmDetailObjFuncUsagePloy("AccountAddrObj", Lists.newArrayList("89645"), null, 0, 100)
        def ploy4 = adminSyncPloyDetailDao.findErpDetailObjFuncUsagePloy("BD_Customer.BD_CUSTCONTACT", null, null, 0, 100)


        println "=========crmMastrer======="
        println ploy1;
        println "=========erpMastrer======="
        println ploy2;

        println "=========crmDetail======="
        println ploy3;
        println "=========erpDetail======="
        println ploy4;
    }

    @Transactional
    def "test"() {
        given:
        def type = chInterfaceMonitorDao.cursorInterfaceMonitorByInterfaceMonitorDataInType("88521",
                "643f7322b54ea80001767d86",
                "ProductObj", null, null, null, new Date(*************), new Date(*************), 1, 10, null, null)
        def a = getCHInterfaceMonitorEntitiesResult("88521", "643f7322b54ea80001767d86", 1, type)
        println(a)

    }
    private static Result<List<CHInterfaceMonitorEntity>> getCHInterfaceMonitorEntitiesResult (String tenantId, String dcId, Integer offset, Cursor<CHInterfaceMonitorEntity> chInterfaceMonitorCursor) {
        List<CHInterfaceMonitorEntity> chInterfaceMonitorEntities = new LinkedList<>();
        if(ObjectUtils.isNotEmpty(chInterfaceMonitorCursor)) {
            long contentSum = 0L;
            //流式处理
            Iterator<CHInterfaceMonitorEntity> interfaceMonitorEntityIterator = chInterfaceMonitorCursor.iterator();
            while (interfaceMonitorEntityIterator.hasNext()) {
                CHInterfaceMonitorEntity interfaceMonitorEntity  =interfaceMonitorEntityIterator.next();
                //判断数据内存大小
                long contentSize = RamUsageEstimateUtil.sizeOfObjectIgnoreException(interfaceMonitorEntity);
                contentSum = contentSum + contentSize;
                if(contentSum > ConfigCenter.CONTENT_LENGTH_LIMIT * 5) {
                    return Result.newError(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR);
                }
                chInterfaceMonitorEntities.add(interfaceMonitorEntity);
            }
        }
        return Result.newSuccess(chInterfaceMonitorEntities);
    }
}
