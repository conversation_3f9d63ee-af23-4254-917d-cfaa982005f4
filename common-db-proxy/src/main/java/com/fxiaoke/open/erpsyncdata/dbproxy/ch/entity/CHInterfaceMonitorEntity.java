package com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CHInterfaceMonitorEntity {
    private String appName; // 服务名称
    private String traceId; // 分布式跟踪id
    private String serverIp; // 发出日志的ip
    private String tenantId; // 租户ei信息
    private Date createTime; // 日志上报时间
    private Date updateTime; // 日志上报时间
    private Date expireTime;//过期时间

    private String logType;//日志类型：sync_data\sync_log\interface_monitor
    private String id;//mongoId
    private String dcId;//数据中心id
    private String objApiName;//真实对象apiName
    private String interfaceMonitorType;//接口类型
    private String arg;//入参
    private String result;//结果数据
    private Integer interfaceMonitorStatus;//调用状态(1.成功 2.失败)
    private Long callTime;//调用时间
    private Long returnTime;//返回时间
    private String remark;//备注
    private Long costTime;//花费时间
    private String syncDataId;//同步记录Id
    private String logId ;//logId
    private String timeFilterArg;//时间筛选参数,
    private Long timeFilterArgStartTime;//开始时间
    private Long timeFilterArgEndTime;//结束时间
    private String timeFilterArgFilters;//筛选条件
    private Integer timeFilterArgOffset ;//偏移量
    private Integer timeFilterArgLimit ;//限制数量


}
