package com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager;


import cn.hutool.core.io.unit.DataSizeUtil;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.ErpSyncLogDTO;
import com.fxiaoke.open.erpsyncdata.common.constant.ProcessInfo2;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao.CHInterfaceMonitorDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ObjectIdTimeUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ibatis.cursor.Cursor;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CHInterfaceMonitorManager {
    @Autowired
    private CHInterfaceMonitorDao chInterfaceMonitorDao;
    @Autowired
    private TenantInfoManager tenantInfoManager;

    public List<String> batchUpsertInterfaceMonitorData(String tenantId, List<InterfaceMonitorData> interfaceMonitorDataList) {//批量上报
        if(CollectionUtils.isEmpty(interfaceMonitorDataList)){
            return Lists.newArrayList();
        }
        List<CHInterfaceMonitorEntity> chInterfaceMonitorEntities=this.buildCHInterfaceMonitorFromInterfaceMoniter(interfaceMonitorDataList);
        sendBizLog(chInterfaceMonitorEntities);
        List<String> ids = chInterfaceMonitorEntities.stream().map(CHInterfaceMonitorEntity::getId).collect(Collectors.toList());
        return ids;
    }
    public int sendBizLog(List<CHInterfaceMonitorEntity> chInterfaceMonitorEntities) {
        if(!CollectionUtils.isEmpty(chInterfaceMonitorEntities)){
            for(CHInterfaceMonitorEntity data:chInterfaceMonitorEntities){
                ErpSyncLogDTO dto= ErpSyncLogDTO.builder()
                        .appName(data.getAppName())// 服务名称
                .traceId(data.getTraceId()) // 分布式跟踪id
                .serverIp(data.getServerIp()) // 发出日志的ip
                .tenantId(data.getTenantId()) // 租户ei信息
                .createTime(data.getCreateTime().getTime()) // 日志上报时间
                .updateTime(data.getUpdateTime().getTime()) // 日志上报时间
                .expireTime(data.getExpireTime().getTime())//过期时间
                .logType(data.getLogType())//日志类型：sync_data\sync_log\interface_monitor
                .id(data.getId())//mongoId
                .dcId(data.getDcId())//数据中心id
                .objApiName(data.getObjApiName())//真实对象apiName
                .interfaceMonitorType(data.getInterfaceMonitorType())//接口类型
                .arg(data.getArg())//入参
                .result(data.getResult())//结果数据
                .interfaceMonitorStatus(data.getInterfaceMonitorStatus())//调用状态(1.成功 2.失败)
                .callTime(data.getCallTime())//调用时间
                .returnTime(data.getReturnTime())//返回时间
                .remark(data.getRemark())//备注
                .costTime(data.getCostTime())//花费时间
                .syncDataId(data.getSyncDataId())//同步记录Id
                .logId(data.getLogId())//logId
                .timeFilterArg(data.getTimeFilterArg())//时间筛选参数,
                .timeFilterArgStartTime(data.getTimeFilterArgStartTime())//开始时间
                .timeFilterArgEndTime(data.getTimeFilterArgEndTime())//结束时间
                .timeFilterArgFilters(data.getTimeFilterArgFilters())//筛选条件
                .timeFilterArgOffset(data.getTimeFilterArgOffset())//偏移量
                .timeFilterArgLimit(data.getTimeFilterArgLimit())//限制数量
                        .build();
                BizLogClient.send("biz_log_erp_sync_logs", Pojo2Protobuf.toMessage(dto, com.fxiaoke.log.ErpSyncLog.class).toByteArray());
            }
        }
        return 0;
    }

    public List<InterfaceMonitorData> listInterfaceMonitorBySyncDataId(String tenantId, String syncDataId,Long startLogTime,Long endLogTime) {
        List<CHInterfaceMonitorEntity> chInterfaceMonitorEntities = chInterfaceMonitorDao.listInterfaceMonitorBySyncDataId(tenantId, syncDataId,new Date(startLogTime),new Date(endLogTime));
        return this.changeChInterfaceMonitorEntityToInterfaceMonitorDataList(chInterfaceMonitorEntities);
    }

    private List<InterfaceMonitorData> changeChInterfaceMonitorEntityToInterfaceMonitorDataList(List<CHInterfaceMonitorEntity> chInterfaceMonitorEntities) {
        List<InterfaceMonitorData> interfaceMonitorDataList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(chInterfaceMonitorEntities)) {
            return interfaceMonitorDataList;
        }
        Set<String> ids= Sets.newHashSet();
        for (CHInterfaceMonitorEntity data : chInterfaceMonitorEntities) {
            if(ids.contains(data.getId())){//去掉id重复的
                continue;
            }
            InterfaceMonitorData interfaceMonitorData = changeChInterfaceMonitorEntityToInterfaceMonitorData(data);
            ids.add(data.getId());
            interfaceMonitorDataList.add(interfaceMonitorData);
        }
        return interfaceMonitorDataList;
    }

    private InterfaceMonitorData changeChInterfaceMonitorEntityToInterfaceMonitorData(CHInterfaceMonitorEntity data) {
        if(data==null){
            return null;
        }
        InterfaceMonitorData interfaceMonitorData = new InterfaceMonitorData();
        interfaceMonitorData.setId(new ObjectId(data.getId()));
        interfaceMonitorData.setTenantId(data.getTenantId());
        interfaceMonitorData.setDcId(data.getDcId());
        interfaceMonitorData.setObjApiName(data.getObjApiName());
        interfaceMonitorData.setType(data.getInterfaceMonitorType());
        interfaceMonitorData.setArg(data.getArg());
        interfaceMonitorData.setResult(data.getResult());
        interfaceMonitorData.setStatus(data.getInterfaceMonitorStatus());
        interfaceMonitorData.setCallTime(data.getCallTime());
        interfaceMonitorData.setReturnTime(data.getReturnTime());
        interfaceMonitorData.setRemark(data.getRemark());
        interfaceMonitorData.setTraceId(data.getTraceId());
        interfaceMonitorData.setCostTime(data.getCostTime());
        interfaceMonitorData.setSyncDataId(data.getSyncDataId());
        interfaceMonitorData.setLogId(data.getLogId());
        if (StringUtils.isNotBlank(data.getTimeFilterArg())) {
            interfaceMonitorData.setTimeFilterArg(JacksonUtil.fromJson(data.getTimeFilterArg(), TimeFilterArg.class));
        }
        interfaceMonitorData.setCreateTime(data.getCreateTime().getTime());
        if(data.getExpireTime()!=null){
            interfaceMonitorData.setExpireTime(data.getExpireTime());
        }
        return interfaceMonitorData;
    }

    public List<InterfaceMonitorData> listInterfaceByIds(String tenantId, List<ObjectId> ids, Integer size) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        Pair<Long, Long> objectIdsTime = ObjectIdTimeUtil.getObjectIdsTime(ids);
        List StrIds = ids.stream().map(id -> id.toString()).collect(Collectors.toList());
        List<CHInterfaceMonitorEntity> chInterfaceMonitorEntities = chInterfaceMonitorDao.listInterfaceByIds(tenantId, StrIds, size,new Date(objectIdsTime.getLeft()),new Date(objectIdsTime.getRight()));
        return this.changeChInterfaceMonitorEntityToInterfaceMonitorDataList(chInterfaceMonitorEntities);
    }


    public Result<List<InterfaceMonitorData>> listInterfaceMonitorByInterfaceMonitorData(String tenantId, String dcId, String objApiName, String type, Integer status, String traceId, Long startTime, Long endTime, Integer offset, Integer limit, Boolean ascending, Long queryTime) {
        if(startTime==null){
            startTime = System.currentTimeMillis()-1000*60*60*24*7L;
        }
        if(endTime==null){
            endTime = System.currentTimeMillis();
        }
        List<CHInterfaceMonitorEntity> chInterfaceMonitorEntities = chInterfaceMonitorDao.listInterfaceMonitorByInterfaceMonitorData(tenantId, dcId,
                objApiName, type, status == null ? null : status.toString(), traceId, new Date(startTime), new Date(endTime), offset, limit, ascending,
                queryTime);
        List<InterfaceMonitorData> list = this.changeChInterfaceMonitorEntityToInterfaceMonitorDataList(chInterfaceMonitorEntities);
        return Result.newSuccess(list);
    }

    public Result<List<InterfaceMonitorData>> listInterfaceMonitorByInterfaceMonitorDataInType(String tenantId, String dcId, String objApiName, List<String> type, Integer status, List<String> objectIds, Long startTime, Long endTime, Integer offset, Integer limit, Boolean ascending, Long queryTime) {
        if(startTime==null){
            startTime = System.currentTimeMillis()-1000*60*60*24*7L;
        }
        if(endTime==null){
            endTime = System.currentTimeMillis();
        }
        List<CHInterfaceMonitorEntity> chInterfaceMonitorEntities = chInterfaceMonitorDao.listInterfaceMonitorByInterfaceMonitorDataInType(tenantId, dcId,
                objApiName, type, status == null ? null : status.toString(), objectIds, new Date(startTime), new Date(endTime), offset, limit, ascending, queryTime);
        List<InterfaceMonitorData> list = this.changeChInterfaceMonitorEntityToInterfaceMonitorDataList(chInterfaceMonitorEntities);
        return Result.newSuccess(list);
    }

    public Result<List<InterfaceMonitorData>> listInterfaceMonitorByResult(String tenantId, String dcId, String objApiName, List<String> type, Integer status, Long startTime, Long endTime, Integer offset, Integer limit, Boolean ascending, Long queryTime, String result) {
        if(startTime==null){
            startTime = System.currentTimeMillis()-1000*60*60*24*7L;
        }
        if(endTime==null){
            endTime = System.currentTimeMillis();
        }
        List<CHInterfaceMonitorEntity> chInterfaceMonitorEntities = chInterfaceMonitorDao.listInterfaceMonitorByResult(tenantId, dcId, objApiName, type,
                status == null ? null : status.toString(), new Date(startTime), new Date(endTime), offset, limit, ascending, queryTime, result);
        List<InterfaceMonitorData> list = this.changeChInterfaceMonitorEntityToInterfaceMonitorDataList(chInterfaceMonitorEntities);
        return Result.newSuccess(list);
    }


    public long countByInterfaceMonitorData(String tenantId, String dcId, String objApiName, String type, Integer status, String traceId, Long startTime, Long endTime, Long queryTime) {
        if(startTime==null){
            startTime = System.currentTimeMillis()-1000*60*60*24*7L;
        }
        if(endTime==null){
            endTime = System.currentTimeMillis();
        }
        return chInterfaceMonitorDao.countByInterfaceMonitorData(tenantId, dcId, objApiName, type, status == null ? null : status.toString(), traceId,
                new Date(startTime), new Date(endTime), queryTime);
    }

    public long countByInterfaceMonitorDataLimitSize(String tenantId, String dcId, String objApiName, List<String> type, Integer status, List<String> objectIds, Long startTime, Long endTime, Long queryTime) {
        if(startTime==null){
            startTime = System.currentTimeMillis()-1000*60*60*24*7L;
        }
        if(endTime==null){
            endTime = System.currentTimeMillis();
        }
        return chInterfaceMonitorDao.countByInterfaceMonitorDataLimitSize(tenantId, dcId, objApiName, type, status == null ? null : status.toString(),
                objectIds, new Date(startTime), new Date(endTime), queryTime);

    }

    public long countByTenantId(String tenantId) {
        return chInterfaceMonitorDao.countByTenantId(tenantId);
    }


    public Result<InterfaceMonitorData> getById(String tenantId, String id) {
        if(id==null){
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
        Pair<Long, Long> objectIdTime = ObjectIdTimeUtil.getObjectIdTime(id);
        CHInterfaceMonitorEntity chInterfaceMonitorEntitie = chInterfaceMonitorDao.getById(tenantId, id,new Date(objectIdTime.getLeft()),new Date(objectIdTime.getRight()));
        if (Objects.nonNull(chInterfaceMonitorEntitie)) {
            InterfaceMonitorData interfaceMonitorData = this.changeChInterfaceMonitorEntityToInterfaceMonitorData(chInterfaceMonitorEntitie);
            return Result.newSuccess(interfaceMonitorData);
        } else {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    public Result<InterfaceMonitorData> getBySyncDataId(String tenantId, String syncDataId,Long startLogTime, Long endLogTime) {
        CHInterfaceMonitorEntity chInterfaceMonitorEntitie = chInterfaceMonitorDao.getBySyncDataId(tenantId, syncDataId,new Date(startLogTime),new Date(endLogTime));
        if (Objects.nonNull(chInterfaceMonitorEntitie)) {
            InterfaceMonitorData interfaceMonitorData = this.changeChInterfaceMonitorEntityToInterfaceMonitorData(chInterfaceMonitorEntitie);
            return Result.newSuccess(interfaceMonitorData);
        } else {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }


    public List<CHInterfaceMonitorEntity> buildCHInterfaceMonitorFromInterfaceMoniter(List<InterfaceMonitorData> interfaceMonitorData) {
        List<CHInterfaceMonitorEntity> logs = Lists.newArrayList();
        for (InterfaceMonitorData data : interfaceMonitorData) {
            CHInterfaceMonitorEntity log = new CHInterfaceMonitorEntity();
            log.setServerIp(ProcessInfo2.serverIp);
            log.setAppName(ProcessInfo2.appName);
            if(data.getId()==null){
                data.setId(new ObjectId());
            }
            log.setId(data.getId().toString());
            log.setTenantId(data.getTenantId());
            log.setLogType("interface_monitor");
            log.setDcId(data.getDcId());
            log.setObjApiName(data.getObjApiName());
            log.setInterfaceMonitorType(data.getType());
            log.setArg(data.getArg());
            log.setResult(data.getResult());
            log.setInterfaceMonitorStatus(data.getStatus());
            log.setCallTime(data.getCallTime());
            log.setReturnTime(data.getReturnTime());
            log.setRemark(data.getRemark());
            log.setTraceId(data.getTraceId());
            log.setCostTime(data.getCostTime());
            log.setSyncDataId(data.getSyncDataId());
            log.setLogId(data.getLogId());
            if (data.getTimeFilterArg() != null) {
                log.setTimeFilterArg(JacksonUtil.toJson(data.getTimeFilterArg()));
                log.setTimeFilterArgStartTime(data.getTimeFilterArg().getStartTime());
                log.setTimeFilterArgEndTime(data.getTimeFilterArg().getEndTime());
                if(data.getTimeFilterArg().getFilters()!=null){
                    log.setTimeFilterArgFilters(JacksonUtil.toJson(data.getTimeFilterArg().getFilters()));
                }

                log.setTimeFilterArgOffset(data.getTimeFilterArg().getOffset());
                log.setTimeFilterArgLimit(data.getTimeFilterArg().getLimit());
            }
            if (data.getCreateTime() != null) {
                log.setCreateTime(new Date(data.getCreateTime()));
            } else {
                log.setCreateTime(new Date());
            }
            log.setUpdateTime(log.getCreateTime());
            log.setExpireTime(getExpireTimeByEi(data.getTenantId(),log.getCreateTime().getTime()));
            logs.add(log);
        }
        return logs;
    }

    private Date getExpireTimeByEi(String tenantId,Long createTime) {
        Long expereTime=null;
        if(createTime!=null){
            expereTime= createTime + tenantInfoManager.getExpireIntervalTimeByEi(tenantId);
        }else{
            expereTime= System.currentTimeMillis() + tenantInfoManager.getExpireIntervalTimeByEi(tenantId);
        }
        return new Date(expereTime);
    }

    public Long findMinDate(String tenantId) {
        return chInterfaceMonitorDao.findMinDate(tenantId);
    }

    public Long deleteBetween(String tenantId, Date beginTime, Date endTime) {
        String lastId=null;
        Long size=0L;
//        while(true){
//            List<CHInterfaceMonitorEntity> result=chInterfaceMonitorDao.listBetween(tenantId, beginTime, endTime,lastId,1000);
//            if(CollectionUtils.isEmpty(result)){
//                break;
//            }
//            lastId=result.get(result.size()-1).getId();
//            for(CHInterfaceMonitorEntity entity:result){
//                entity.setExpireTime(new Date());//利用合并，利用过期时间删除，因为不允许delete
//            }
//            sendBizLog(result);
//            size+=result.size();
//            if(result.size()<1000){
//                break;
//            }
//        }
        return size;
    }

    @Transactional
    public Result<List<InterfaceMonitorData>> cursorInterfaceMonitorByInterfaceMonitorDataInType(String tenantId, String dcId, String objApiName, List<String> type, Integer status, List<String> objectIds, Long startTime, Long endTime, Integer offset, Integer limit, Boolean ascending, Long queryTime) {
        //兜底
        if (startTime == null){
            startTime = System.currentTimeMillis()-1000*60*60*24*7L;
        }
        if (endTime == null){
            endTime = System.currentTimeMillis();
        }
        Cursor<CHInterfaceMonitorEntity> chInterfaceMonitorCursor = chInterfaceMonitorDao.cursorInterfaceMonitorByInterfaceMonitorDataInType(tenantId, dcId,
                objApiName, type, status == null ? null : status.toString(), objectIds, new Date(startTime), new Date(endTime), offset, limit, ascending, queryTime);

        Result<List<CHInterfaceMonitorEntity>> chInterfaceMonitorEntitiesResult = getCHInterfaceMonitorEntitiesResult(tenantId, dcId, offset, chInterfaceMonitorCursor);

        if (!chInterfaceMonitorEntitiesResult.isSuccess()) {
            return Result.copy(chInterfaceMonitorEntitiesResult);
        }

        List<InterfaceMonitorData> list = this.changeChInterfaceMonitorEntityToInterfaceMonitorDataList(chInterfaceMonitorEntitiesResult.getData());
        return Result.newSuccess(list);
    }

    private Result<List<CHInterfaceMonitorEntity>> getCHInterfaceMonitorEntitiesResult (String tenantId, String dcId, Integer offset, Cursor<CHInterfaceMonitorEntity> chInterfaceMonitorCursor) {
        List<CHInterfaceMonitorEntity> chInterfaceMonitorEntities = new LinkedList<>();
        if(ObjectUtils.isNotEmpty(chInterfaceMonitorCursor)) {
            long contentSum = 0L;
            //流式处理
            Iterator<CHInterfaceMonitorEntity> interfaceMonitorEntityIterator = chInterfaceMonitorCursor.iterator();
            while (interfaceMonitorEntityIterator.hasNext()) {
                CHInterfaceMonitorEntity interfaceMonitorEntity  =interfaceMonitorEntityIterator.next();
                //判断数据内存大小
                long contentSize = RamUsageEstimateUtil.sizeOfObjectIgnoreException(interfaceMonitorEntity);
                contentSum = contentSum + contentSize;
                if(contentSum > ConfigCenter.CONTENT_LENGTH_LIMIT * 5) {
                    log.warn("!!!日志数据累加起来太大, 报错提示,ei:{}, dcId:{}, offset:{},size:{}", tenantId, dcId, offset, DataSizeUtil.format(contentSum)); // ignoreI18n
                    return Result.newError(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR);
                }
                chInterfaceMonitorEntities.add(interfaceMonitorEntity);
            }
        }
        return Result.newSuccess(chInterfaceMonitorEntities);
    }
}
