package com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao;


import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 日志类查询一定要带日期条件，否则会全表扫描
 */

@Repository
public interface CHInterfaceMonitorDao {

    List<CHInterfaceMonitorEntity> listInterfaceMonitorBySyncDataId(@Param(value = "tenantId") String tenantId, @Param(value = "syncDataId") String syncDataId,
                                                                    @Param(value = "startLogTime") Date startLogTime,
                                                                    @Param(value = "endTLogTime") Date endLogTime);

    List<CHInterfaceMonitorEntity> listInterfaceByIds(@Param(value = "tenantId") String tenantId, @Param(value = "ids") List<String> ids,
                                                      @Param(value = "limit") Integer limit,
                                                      @Param(value = "startLogTime") Date startLogTime,
                                                      @Param(value = "endTLogTime") Date endLogTime);

    List<CHInterfaceMonitorEntity> listInterfaceMonitorByInterfaceMonitorData(@Param(value = "tenantId") String tenantId,
                                                                              @Param(value = "dcId") String dcId,
                                                                              @Param(value = "objApiName") String objApiName,
                                                                              @Param(value = "type") String type,
                                                                              @Param(value = "status") String status,
                                                                              @Param(value = "traceId") String traceId,
                                                                              @Param(value = "startTime") Date startTime,
                                                                              @Param(value = "endTime") Date endTime,
                                                                              @Param(value = "offset") Integer offset,
                                                                              @Param(value = "limit") Integer limit,
                                                                              @Param(value = "ascending") Boolean ascending,
                                                                              @Param(value = "queryTime") Long queryTime);

    List<CHInterfaceMonitorEntity> listInterfaceMonitorByInterfaceMonitorDataInType(@Param(value = "tenantId") String tenantId,
                                                                                    @Param(value = "dcId") String dcId,
                                                                                    @Param(value = "objApiName") String objApiName,
                                                                                    @Param(value = "type") List<String> type,
                                                                                    @Param(value = "status") String status,
                                                                                    @Param(value = "objectIds") List<String> objectIds,
                                                                                    @Param(value = "startTime") Date startTime,
                                                                                    @Param(value = "endTime") Date endTime,
                                                                                    @Param(value = "offset") Integer offset,
                                                                                    @Param(value = "limit") Integer limit,
                                                                                    @Param(value = "ascending") Boolean ascending,
                                                                                    @Param(value = "queryTime") Long queryTime);

    List<CHInterfaceMonitorEntity> listInterfaceMonitorByResult(@Param(value = "tenantId") String tenantId, @Param(value = "dcId") String dcId,
                                                                @Param(value = "objApiName") String objApiName, @Param(value = "type") List<String> type,
                                                                @Param(value = "status") String status, @Param(value = "startTime") Date startTime,
                                                                @Param(value = "endTime") Date endTime, @Param(value = "offset") Integer offset,
                                                                @Param(value = "limit") Integer limit, @Param(value = "ascending") Boolean ascending,
                                                                @Param(value = "queryTime") Long queryTime, @Param(value = "result") String result);

    long countByInterfaceMonitorData(@Param(value = "tenantId") String tenantId, @Param(value = "dcId") String dcId,
                                     @Param(value = "objApiName") String objApiName, @Param(value = "type") String type,
                                     @Param(value = "status") String status, @Param(value = "traceId") String traceId,
                                     @Param(value = "startTime") Date startTime, @Param(value = "endTime") Date endTime,
                                     @Param(value = "queryTime") Long queryTime);

    long countByInterfaceMonitorDataLimitSize(@Param(value = "tenantId") String tenantId, @Param(value = "dcId") String dcId,
                                              @Param(value = "objApiName") String objApiName, @Param(value = "type") List<String> type,
                                              @Param(value = "status") String status, @Param(value = "objectIds") List<String> objectIds,
                                              @Param(value = "startTime") Date startTime, @Param(value = "endTime") Date endTime,
                                              @Param(value = "queryTime") Long queryTime);

    long countByTenantId(@Param(value = "tenantId") String tenantId);

    CHInterfaceMonitorEntity getById(@Param(value = "tenantId") String tenantId, @Param(value = "id") String id,
                                     @Param(value = "startLogTime") Date startLogTime,
                                     @Param(value = "endTLogTime") Date endLogTime);

    CHInterfaceMonitorEntity getBySyncDataId(@Param(value = "tenantId") String tenantId, @Param(value = "syncDataId") String syncDataId,
                                             @Param(value = "startLogTime") Date startLogTime,
                                             @Param(value = "endTLogTime") Date endLogTime);

    Long findMinDate(@Param(value = "tenantId") String tenantId);

    List<CHInterfaceMonitorEntity> listBetween(@Param(value = "tenantId") String tenantId, @Param(value = "beginTime") Date beginTime, @Param(value = "endTime") Date endTime,
                                               @Param(value = "lastId") String lastId, @Param(value = "limit") Integer limit);

    /**
     * Cursor：流式处理，集成了Iterable，可以遍历查询出来的每条数据，即每次取出一条放在内存
     * 使用方式一：需要配合事务 @Transactional
     */
    Cursor<CHInterfaceMonitorEntity> cursorInterfaceMonitorByInterfaceMonitorDataInType(@Param(value = "tenantId") String tenantId,
                                                                                        @Param(value = "dcId") String dcId,
                                                                                        @Param(value = "objApiName") String objApiName,
                                                                                        @Param(value = "type") List<String> type,
                                                                                        @Param(value = "status") String status,
                                                                                        @Param(value = "objectIds") List<String> objectIds,
                                                                                        @Param(value = "startTime") Date startTime,
                                                                                        @Param(value = "endTime") Date endTime,
                                                                                        @Param(value = "offset") Integer offset,
                                                                                        @Param(value = "limit") Integer limit,
                                                                                        @Param(value = "ascending") Boolean ascending,
                                                                                        @Param(value = "queryTime") Long queryTime);
}