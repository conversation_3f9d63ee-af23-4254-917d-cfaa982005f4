<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao.CHInterfaceMonitorDao">
    <sql id="Base_Column_List">
        appName,
        traceId,
        serverIp,
        tenantId,
        createTime,
        updateTime,
        expireTime,
        logType,
        id,
        dcId,
        objApiName,
        interfaceMonitorType,
        arg,
        "result",
        interfaceMonitorStatus,
        callTime,
        returnTime,
        remark,
        costTime,
        syncDataId,
        logId,
        timeFilterArg,
        timeFilterArgStartTime,
        timeFilterArgEndTime,
        timeFilterArgFilters,
        timeFilterArgOffset,
        timeFilterArgLimit
    </sql>
    <sql id="Not_Result_Column_List">
        appName,
        traceId,
        serverIp,
        tenantId,
        createTime,
        updateTime,
        expireTime,
        logType,
        id,
        dcId,
        objApiName,
        interfaceMonitorType,
        arg,
        interfaceMonitorStatus,
        callTime,
        returnTime,
        remark,
        costTime,
        syncDataId,
        logId,
        timeFilterArg,
        timeFilterArgStartTime,
        timeFilterArgEndTime,
        timeFilterArgFilters,
        timeFilterArgOffset,
        timeFilterArgLimit
    </sql>
    <select id="listInterfaceMonitorBySyncDataId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='interface_monitor'
        and syncDataId= #{syncDataId}
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
        limit 1000
    </select>

    <select id="listInterfaceByIds" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='interface_monitor'
        and id IN
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
        limit #{limit}
    </select>
    <select id="listInterfaceMonitorByInterfaceMonitorData" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='interface_monitor'
        <if test="dcId!=null and dcId!=''">
            and dcId = #{dcId}
        </if>
        <if test="objApiName!=null and objApiName!=''">
            and objApiName = #{objApiName}
        </if>
        <if test="type!=null and type!=''">
            and interfaceMonitorType = #{type}
        </if>
        <if test="status!=null">
            and interfaceMonitorStatus = #{status}
        </if>
        <if test="traceId!=null and traceId!=''">
            and traceId = #{traceId}
        </if>
        <if test="startTime!=null">
            and createTime >= #{startTime}
        </if>
        <if test="endTime!=null">
            and createTime &lt;= #{endTime}
        </if>
        <if test="queryTime!=null">
            and timeFilterArgStartTime &lt;= #{queryTime}
            and timeFilterArgEndTime >= #{queryTime}
        </if>
        <choose>
            <when test="ascending!=null and ascending">
                order by createTime asc
            </when>
            <otherwise>
                order by createTime desc
            </otherwise>
        </choose>
        limit #{limit} offset #{offset}
    </select>
    <select id="listInterfaceMonitorByInterfaceMonitorDataInType" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity">
        select <include refid="Not_Result_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='interface_monitor'
        <if test="dcId!=null and dcId!=''">
            and dcId = #{dcId}
        </if>
        <if test="objApiName!=null and objApiName!=''">
            and objApiName = #{objApiName}
        </if>
        <if test="type!=null and type.size()>0">
            and interfaceMonitorType   IN
            <foreach item="item" collection="type" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="status!=null">
            and interfaceMonitorStatus = #{status}
        </if>
        <if test="objectIds!=null and objectIds.size()>0">
            and id IN
            <foreach item="item" collection="objectIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime!=null">
            and createTime >= #{startTime}
        </if>
        <if test="endTime!=null">
            and createTime &lt;= #{endTime}
        </if>
        <if test="queryTime!=null">
            and timeFilterArgStartTime &lt;= #{queryTime}
            and timeFilterArgEndTime >= #{queryTime}
        </if>
        <choose>
            <when test="ascending!=null and ascending">
                order by createTime asc
            </when>
            <otherwise>
                order by createTime desc
            </otherwise>
        </choose>
        limit #{limit} offset #{offset}
    </select>
    <select id="listInterfaceMonitorByResult" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='interface_monitor'
        <if test="dcId!=null and dcId!=''">
            and dcId = #{dcId}
        </if>
        <if test="objApiName!=null and objApiName!=''">
            and objApiName = #{objApiName}
        </if>
        <if test="type!=null and type.size()>0">
            and interfaceMonitorType  IN
            <foreach item="item" collection="type" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="status!=null and status!=''">
            and interfaceMonitorStatus = #{status}
        </if>
        <if test="startTime!=null">
            and createTime >= #{startTime}
        </if>
        <if test="endTime!=null">
            and createTime &lt;= #{endTime}
        </if>
        <if test="queryTime!=null">
            and timeFilterArgStartTime &lt;= #{queryTime}
            and timeFilterArgEndTime >= #{queryTime}
        </if>
        <if test="result!=null and result!=''">
            and result like CONCAT('%', #{result}, '%')
        </if>
        <choose>
            <when test="ascending!=null and ascending">
                order by createTime asc
            </when>
            <otherwise>
                order by createTime desc
            </otherwise>
        </choose>
        limit #{limit} offset #{offset}
    </select>
    <select id="countByInterfaceMonitorData" resultType="java.lang.Long">
        select count(*)
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='interface_monitor'
        <if test="dcId!=null and dcId!=''">
            and dcId = #{dcId}
        </if>
        <if test="objApiName!=null and objApiName!=''">
            and objApiName = #{objApiName}
        </if>
        <if test="type!=null and type!=''">
            and interfaceMonitorType = #{type}
        </if>
        <if test="status!=null">
            and interfaceMonitorStatus = #{status}
        </if>
        <if test="traceId!=null and traceId!=''">
            and traceId = #{traceId}
        </if>
        <if test="startTime!=null">
            and createTime >= #{startTime}
        </if>
        <if test="endTime!=null">
            and createTime &lt;= #{endTime}
        </if>
        <if test="queryTime!=null">
            and timeFilterArgStartTime &lt;= #{queryTime}
            and timeFilterArgEndTime >= #{queryTime}
        </if>
    </select>
    <select id="countByInterfaceMonitorDataLimitSize" resultType="java.lang.Long">
        select count(id) from
        (select id
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='interface_monitor'
        <if test="dcId!=null and dcId!=''">
            and dcId = #{dcId}
        </if>
        <if test="objApiName!=null and objApiName!=''">
            and objApiName = #{objApiName}
        </if>
        <if test="type!=null and type.size()>0">
            and interfaceMonitorType IN
            <foreach item="item" collection="type" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="status!=null">
            and interfaceMonitorStatus = #{status}
        </if>
        <if test="objectIds!=null and objectIds.size()>0">
            and id IN
            <foreach item="item" collection="objectIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime!=null">
            and createTime >= #{startTime}
        </if>
        <if test="endTime!=null">
            and createTime &lt;= #{endTime}
        </if>
        <if test="queryTime!=null">
            and timeFilterArgStartTime &lt;= #{queryTime}
            and timeFilterArgEndTime >= #{queryTime}
        </if>
        limit 1000)as t
    </select>
    <select id="getById" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='interface_monitor'
          and id= #{id}
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
        limit 1
    </select>
    <select id="getBySyncDataId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='interface_monitor'
          and syncDataId= #{syncDataId}
        and createTime >= #{startLogTime}
        and createTime &lt;= #{endTLogTime}
        limit 1
    </select>

    <select id="countByTenantId" resultType="java.lang.Long">
        select count(*)
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='interface_monitor'
    </select>
    <select id="findMinDate" resultType="java.lang.Long">
        select createTime
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId}
          and logType = 'interface_monitor'
        order by createTime asc limit 1
    </select>
    <select id="listBetween" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='interface_monitor'
        <if test="startTime!=null">
            createTime>=#{startTime}
        </if>
        <if test="endTime!=null">
            createTime &lt; #{endTime}
        </if>
        <if test="lastId!=null">
            id > #{lastId}
        </if>
        order by id asc
        limit #{limit}
    </select>

    <select id="cursorInterfaceMonitorByInterfaceMonitorDataInType" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity">
        select <include refid="Base_Column_List"/>
        from biz_log_erp_sync_log_dist
        where tenantId = #{tenantId} and logType='interface_monitor'
        <if test="dcId!=null and dcId!=''">
            and dcId = #{dcId}
        </if>
        <if test="objApiName!=null and objApiName!=''">
            and objApiName = #{objApiName}
        </if>
        <if test="type!=null and type.size()>0">
            and interfaceMonitorType   IN
            <foreach item="item" collection="type" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="status!=null">
            and interfaceMonitorStatus = #{status}
        </if>
        <if test="objectIds!=null and objectIds.size()>0">
            and id IN
            <foreach item="item" collection="objectIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime!=null">
            and createTime >= #{startTime}
        </if>
        <if test="endTime!=null">
            and createTime &lt;= #{endTime}
        </if>
        <if test="queryTime!=null">
            and timeFilterArgStartTime &lt;= #{queryTime}
            and timeFilterArgEndTime >= #{queryTime}
        </if>
        <choose>
            <when test="ascending!=null and ascending">
                order by createTime asc
            </when>
            <otherwise>
                order by createTime desc
            </otherwise>
        </choose>
        limit #{limit} offset #{offset}
    </select>
</mapper>