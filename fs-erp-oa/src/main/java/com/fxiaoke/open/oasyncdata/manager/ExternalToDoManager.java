package com.fxiaoke.open.oasyncdata.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.restful.common.StopWatch;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.UpdateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.CreateTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DealTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DeleteTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.UpdateTodoResult;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.*;

import com.fxiaoke.open.oasyncdata.mongo.OASyncLogMappingsDao;
import com.fxiaoke.open.oasyncdata.mongo.OASyncLogSnapshotDao;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.dao.OASyncApiDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.entity.OASyncApiEntity;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogMappingDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogSnapshotDoc;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.util.BeanUtil;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.model.OAConnectParam;
import com.fxiaoke.open.oasyncdata.model.OARequestModel;
import com.fxiaoke.open.oasyncdata.model.QueryOASyncLogArg;
import com.fxiaoke.open.oasyncdata.result.Result2;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.util.BuriedSitesStatisticsUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/11/8 17:09 处理CRM代办流程
 * @Version 1.0
 */
@Component
@Slf4j
public class ExternalToDoManager {
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;

    @Autowired
    private OASyncLogMappingsDao oaSyncLogMappingsDao;
    @Autowired
    private OASyncLogSnapshotDao oaSyncLogSnapshotDao;

    @Autowired
    private OASyncApiDao oaSyncApiDao;

    @Autowired
    private ApprovalTaskManager approvalTaskManager;

    @Autowired
    private OACommonFieldManager oACommonFieldManager;

    @Autowired
    private OARequestManager oaRequestManager;

    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ExternalToDoManager externalToDoManager;
    @Autowired
    private I18NStringManager i18NStringManager;



    public CreateTodoResult createTodo(CreateTodoArg createTodoArg,boolean autoRetry,String dataCenterId) {
        String createTodoArgJson = new Gson().toJson(createTodoArg);
        log.info("ExternalToDoManager createTodo json:{}", createTodoArgJson);
        String tenantId = String.valueOf(createTodoArg.getEi());
        CreateTodoResult createTodoResult = new CreateTodoResult();
        createTodoResult.setCode(200);
        ObjectApiEnum objectApiEnum = ObjectApiEnum.getObjApiEnumByBizType(createTodoArg.getBizType());
        if (objectApiEnum == null) {
            return createTodoResult;
        }
        //判断是否为空
        List<OAConnectInfoEntity> oaConnectInfoEntities=null;
        if(StringUtils.isNotEmpty(dataCenterId)){
            OAConnectInfoEntity oaConnectInfoById = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getOAConnectInfoById(tenantId, dataCenterId);
            oaConnectInfoEntities=Lists.newArrayList(oaConnectInfoById);
        }else{
            oaConnectInfoEntities=oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
        }

        for (OAConnectInfoEntity oaConnectInfoEntity : oaConnectInfoEntities) {
            OASyncApiEntity oaSyncApiEntity = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId, oaConnectInfoEntity.getId(),OAEventEnum.CREATE.getEventStatus(),
                    objectApiEnum.getObjApiName());
            if (oaSyncApiEntity == null || oaConnectInfoEntity == null) {
                continue;
            }

            // 插入同步中日志
            ObjectId logId = ObjectId.get();
             dataCenterId=oaConnectInfoEntity.getId();
            ObjectApiEnum objApiEnumByBizType = ObjectApiEnum.getObjApiEnumByBizType(createTodoArg.getBizType());
            //查询detail
            String objDataId=createTodoArg.getExtraDataMap().get("objectId");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult = approvalTaskManager.queryBizDataByType(tenantId, dataCenterId,createTodoArg.getSourceId(), objApiEnumByBizType.getObjApiName(),objDataId,objApiEnumByBizType.getBizType(),EventTypeEnum.ADD.getType());
            if (describeResult.getCode()==201112008) {//数据已作废或已删除的code
                log.warn("get data is null，describeResult={}",describeResult);
                return createTodoResult;
            }
            ObjectData objectData = describeResult.getData().getData();
            objectData.put("sourceId",createTodoArg.getSourceId());
            log.info("createTodo objectData:{}" , JSONObject.toJSONString(objectData));

            //组装title,不同业务类型标识的title字段不一致
            Map<String, String> nameMap = oACommonFieldManager.builderTitle(objectData, oaSyncApiEntity,createTodoArg.getBizType(),dataCenterId);
            OASyncLogSnapshotDoc oaSyncLogEntity =
                    OASyncLogSnapshotDoc.builder().tenantId(tenantId).dataId(createTodoArg.getSourceId()).businessDataId(objDataId).businessType(CrmTypeMessageEnum.CRM_TODO_TYPE.getType()).
                            status(OASyncLogEnum.SYNC.getSyncType()).id(logId).
                            updateTime(new Date()).createTime(new Date()).objApiName(objectApiEnum.getObjApiName()).dataName(objectData.getName()).
                            eventType(OAEventEnum.CREATE.getEventStatus()).messageType(OAMessageTag.CREATE_TO_DO_TAG).message(i18NStringManager.getByEi(I18NStringEnum.s667,tenantId)).title(nameMap.get("data_name")).objectName(nameMap.get("object_name"))
                            .dataName(nameMap.get("data_name")).dataCenterId(oaConnectInfoEntity.getId()).dataJson(createTodoArgJson).build();
            //插入mapping表
            OASyncLogMappingDoc mappingDoc = BeanUtil.copy(oaSyncLogEntity, OASyncLogMappingDoc.class);
            ObjectId mappingDocId=queryMappingIdByDoc(mappingDoc);
            oaSyncLogSnapshotDao.saveLog(tenantId,oaSyncLogEntity,mappingDocId,autoRetry);
            OAConnectParam oaConnectParam = GsonUtil.fromJson(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
            try {
                // json字段转换
                // 另外manager,避免影响
                List<OARequestModel> oaRequestModelList = oACommonFieldManager.exchangeOAField(tenantId, oaConnectInfoEntity.getId(),oaSyncApiEntity, describeResult.getData(), createTodoArg.getReceiverIds());
                for (OARequestModel oaRequestModel : oaRequestModelList) {
                    // 替换布局字段
                    oACommonFieldManager.exchangeLayoutField(createTodoArg, oaRequestModel);
                    Result<String> result = oaRequestManager.callCustomFuncAndRestfulService(tenantId,
                            oaConnectParam,
                            oaRequestModel,
                            oaSyncApiEntity.getUrl(),
                            oaSyncApiEntity.getRequestMode(),
                            dataCenterId,
                            oaSyncLogEntity);
                    log.info("callCustomFuncAndRestfulService external oa {},result:{}",oaRequestModel,result);
                    ObjectId afterObjectId=ObjectId.get();
                    if (!result.isSuccess()) {
                        log.warn("createTodo error,data={},result={}", new Gson().toJson(createTodoArg), result);
                        String status=result.getErrCode().equals(ResultCodeEnum.OA_NOT_SYNC.getErrCode())?OASyncLogEnum.NOT_SYNC.getSyncType():OASyncLogEnum.FAIL.getSyncType();
                        OASyncLogSnapshotDoc failOASyncLogEntity =
                                OASyncLogSnapshotDoc.builder().tenantId(tenantId).dataId(createTodoArg.getSourceId()).businessType(CrmTypeMessageEnum.CRM_TODO_TYPE.getType()).businessDataId(objDataId).status(status).id(afterObjectId).
                                        updateTime(new Date()).createTime(new Date()).objApiName(objectApiEnum.getObjApiName()).receiverId(oaRequestModel.getReceiverId()).
                                        eventType(OAEventEnum.CREATE.getEventStatus()).messageType(OAMessageTag.CREATE_TO_DO_TAG).title(nameMap.get("data_name")).objectName(nameMap.get("object_name"))
                                        .dataName(nameMap.get("data_name")).dataCenterId(oaConnectInfoEntity.getId()).message(result.getErrMsg()).dataJson(oaRequestModel.getRequestJson()).build();
                        failOASyncLogEntity.setUrl(oaSyncLogEntity.getUrl());
                        failOASyncLogEntity.setAplApiName(oaSyncLogEntity.getAplApiName());
                        failOASyncLogEntity.setMethod(oaSyncLogEntity.getMethod());
                        failOASyncLogEntity.setHeader(oaSyncLogEntity.getHeader());
                        failOASyncLogEntity.setBody(oaSyncLogEntity.getBody());
                        failOASyncLogEntity.setResponse(oaSyncLogEntity.getResponse());
                        log.info("external oa {},result:{},insert",oaRequestModel,failOASyncLogEntity);
                        mappingDoc.setStatus(OASyncLogEnum.FAIL.getSyncType());
                        //根据配置的规则，存储失败的数据，进行重试
                        oaSyncLogSnapshotDao.saveLog(tenantId,failOASyncLogEntity,null,autoRetry);
                    } else {
                        OASyncLogSnapshotDoc successLogEntity =
                                OASyncLogSnapshotDoc.builder().tenantId(tenantId).dataId(createTodoArg.getSourceId()).businessType(CrmTypeMessageEnum.CRM_TODO_TYPE.getType()).businessDataId(objDataId).status(OASyncLogEnum.SUCCESS.getSyncType()).id(afterObjectId).
                                        updateTime(new Date()).createTime(new Date()).objApiName(objectApiEnum.getObjApiName()).receiverId(oaRequestModel.getReceiverId()).
                                        eventType(OAEventEnum.CREATE.getEventStatus()).messageType(OAMessageTag.CREATE_TO_DO_TAG).title(nameMap.get("data_name")).objectName(nameMap.get("object_name"))
                                        .dataName(nameMap.get("data_name")).dataCenterId(oaConnectInfoEntity.getId()).message(result.getErrMsg()).dataJson(oaRequestModel.getRequestJson()).build();
                        successLogEntity.setUrl(oaSyncLogEntity.getUrl());
                        successLogEntity.setAplApiName(oaSyncLogEntity.getAplApiName());
                        successLogEntity.setMethod(oaSyncLogEntity.getMethod());
                        successLogEntity.setHeader(oaSyncLogEntity.getHeader());
                        successLogEntity.setBody(oaSyncLogEntity.getBody());
                        successLogEntity.setResponse(oaSyncLogEntity.getResponse());
                        log.info("external oa {},result:{},insert",oaRequestModel,successLogEntity);
                        mappingDoc.setStatus(OASyncLogEnum.SUCCESS.getSyncType());
                        oaSyncLogSnapshotDao.saveLog(tenantId,successLogEntity,null,autoRetry);

                    }
                    // 上传神策
                    BuriedSitesStatisticsUtil.uploadBuriedStitesLog(tenantId, objectApiEnum.getObjApiName());
                }
                oaSyncLogSnapshotDao.deleteByObjectId(tenantId,logId);
                oaSyncLogMappingsDao.deleteByObjectId(tenantId,mappingDocId);

            } catch (Throwable e) {
                log.error("createTodo error message.", e);
                OASyncLogSnapshotDoc exceptionEntity =
                        OASyncLogSnapshotDoc.builder().tenantId(tenantId).dataId(createTodoArg.getSourceId()).businessType(CrmTypeMessageEnum.CRM_TODO_TYPE.getType()).businessDataId(objDataId).status(OASyncLogEnum.EXCEPTION.getSyncType()).id(logId).
                                updateTime(new Date()).createTime(new Date()).objApiName(objectApiEnum.getObjApiName()).
                                eventType(OAEventEnum.CREATE.getEventStatus()).messageType(OAMessageTag.CREATE_TO_DO_TAG).title(nameMap.get("data_name")).objectName(nameMap.get("object_name"))
                                .dataName(nameMap.get("data_name")).dataCenterId(oaConnectInfoEntity.getId()).message(e.getMessage()).dataJson(GsonUtil.toJson(createTodoArg)).build();
                exceptionEntity.setUrl(oaSyncLogEntity.getUrl());
                exceptionEntity.setAplApiName(oaSyncLogEntity.getAplApiName());
                exceptionEntity.setMethod(oaSyncLogEntity.getMethod());
                exceptionEntity.setHeader(oaSyncLogEntity.getHeader());
                exceptionEntity.setBody(oaSyncLogEntity.getBody());
                exceptionEntity.setResponse(oaSyncLogEntity.getResponse());
                oaSyncLogSnapshotDao.updateByObjectId(tenantId,logId,exceptionEntity);
                mappingDoc.setStatus(OASyncLogEnum.EXCEPTION.getSyncType());
                mappingDoc.setLastSyncLogId(logId);
                oaSyncLogMappingsDao.updateByObjectId(tenantId,mappingDocId,mappingDoc);
            }
        }


        return createTodoResult;
    }


    public UpdateTodoResult updateTodo(UpdateTodoArg updateTodoArg) {
        UpdateTodoResult updateTodoResult = new UpdateTodoResult();
        updateTodoResult.setCode(200);
        String tenantId = String.valueOf(updateTodoArg.getEi());
        log.info("updateTodo json:{},ei:{}", new Gson().toJson(updateTodoArg), tenantId);
        return updateTodoResult;
    }
    //dealTodo json:{"ea":"88521","ei":88521,"operators":[1013,1000,1019],"sourceId":"64f94b78426d3a53a45d230b","bizType":"452","handleUserIds":[1000],"generateUrlType":0,"extraDataMap":{},"groupKeys":[]}
    public DealTodoResult dealTodo(DealTodoArg dealTodoArg,boolean autoRetry,String dataCenterId) {
        //operators 处理的人
        String dealTodoArgJson = new Gson().toJson(dealTodoArg);
        log.info(" ExternalToDoManager dealTodo json:{}", dealTodoArgJson);
        DealTodoResult dealTodoResult = new DealTodoResult();
        dealTodoResult.setCode(200);
        String tenantId = String.valueOf(dealTodoArg.getEi());
        ObjectApiEnum objectApiEnum = ObjectApiEnum.getObjApiEnumByBizType(dealTodoArg.getBizType());
        if (objectApiEnum == null) {
            return dealTodoResult;
        }
        StopWatch stopWatch = StopWatch.create("dealtoDo");
        //判断配置
        List<OAConnectInfoEntity> oaConnectInfoEntities=null;
        if(StringUtils.isNotEmpty(dataCenterId)){
            OAConnectInfoEntity oaConnectInfoById = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getOAConnectInfoById(tenantId, dataCenterId);
            oaConnectInfoEntities=Lists.newArrayList(oaConnectInfoById);
        }else{
            oaConnectInfoEntities=oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
        }
        for (OAConnectInfoEntity oaConnectInfoEntity : oaConnectInfoEntities) {
            OASyncApiEntity oaSyncApiEntity = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId, oaConnectInfoEntity.getId(),OAEventEnum.DEAL.getEventStatus(),
                    objectApiEnum.getObjApiName());

            if (oaSyncApiEntity == null || oaConnectInfoEntity == null) {
                continue;
            }
            stopWatch.lap("querySetting");
            // 插入同步中日志
            ObjectId logId = ObjectId.get();
            dataCenterId = oaConnectInfoEntity.getId();

            ObjectApiEnum objApiEnumByBizType = ObjectApiEnum.getObjApiEnumByBizType(dealTodoArg.getBizType());
            //查询detail

            String objDataId=dealTodoArg.getExtraDataMap().get("objectId");//这里update的时候，消息平台不会透传objDataId.
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult = approvalTaskManager.queryBizDataByType(tenantId,dataCenterId, dealTodoArg.getSourceId(), objApiEnumByBizType.getObjApiName(),objDataId,objApiEnumByBizType.getBizType(),EventTypeEnum.UPDATE.getType());
            if (describeResult.getCode()==201112008) {//数据已作废或已删除的code
                log.warn("get data is null，describeResult={}",describeResult);
                return dealTodoResult;
            }
            ObjectData objectData = describeResult.getData().getData();
            objectData.put("sourceId",dealTodoArg.getSourceId());//消息平台的唯一性
            objDataId=objectData.getId();
            log.info("dealTodo objectData:{}" ,  JSONObject.toJSONString(objectData));
            stopWatch.lap("queryCrm");
            boolean dealToDelete = false;
            //判断审批流的状态，如果是撤回则进行删除代办的操作
            if(dealTodoArg.getBizType().equals(ObjectApiEnum.FS_APPROVAL_TASK_OBJ.getBizType())){
                if("cancel".equals(objectData.get("state"))||("reject".equals(objectData.get("state")))){
                    //调用删除接口
                    DeleteTodoArg deleteTodoArg=new DeleteTodoArg();
                    deleteTodoArg.setBizType(dealTodoArg.getBizType());
                    deleteTodoArg.setEa(dealTodoArg.getEa());
                    deleteTodoArg.setEi(dealTodoArg.getEi());
                    deleteTodoArg.setSourceId(dealTodoArg.getSourceId());
                    //如果是驳回，删除未处理人的代办
                    if("reject".equals(objectData.get("state"))){
                        List<Integer> deleteEmpIds = (List<Integer>) objectData.get("current_candidate_ids");
                        if(CollectionUtils.isNotEmpty(deleteEmpIds)){
                            deleteTodoArg.setDeleteEmployeeIds(deleteEmpIds);
                            deleteTodo(deleteTodoArg,autoRetry,dataCenterId);
                        }
                    }else{
                        deleteTodoArg.setDeleteEmployeeIds(dealTodoArg.getOperators());
                        deleteTodo(deleteTodoArg,autoRetry,dataCenterId);
                        return dealTodoResult;
                    }
                }
                try{
                    // 多人审批场景下，一人审批后，未审批的其他人的处理待办转为删除待办
                    String flowDealDeleteTenant = ConfigFactory.getInstance().
                            getConfig(OAConfigEnum.FS_ERP_SYNC_OA_MQ.getName()).get(OAConfigEnum.FLOW_DEAL_DELATE_TENANT.getName());
                    if(flowDealDeleteTenant != null){
                        List<String> tenantIds = Arrays.asList(flowDealDeleteTenant.split(";"));
                        if(tenantIds.contains(tenantId)){
                            dealToDelete = true;
                        }
                    }
                    if(dealToDelete){
                        List<Integer> disUserIds = CollectionUtils.disjunction(dealTodoArg.getOperators(), dealTodoArg.getHandleUserIds()).stream().collect(Collectors.toList());
                        //调用删除待办接口,handlerUser操作人，影响人Operators
                        DeleteTodoArg deleteTodoArg=new DeleteTodoArg();
                        deleteTodoArg.setBizType(dealTodoArg.getBizType());
                        deleteTodoArg.setEa(dealTodoArg.getEa());
                        deleteTodoArg.setEi(dealTodoArg.getEi());
                        deleteTodoArg.setSourceId(dealTodoArg.getSourceId());
                        deleteTodoArg.setDeleteEmployeeIds(disUserIds);
                        deleteTodo(deleteTodoArg,autoRetry,dataCenterId);
                    }
                }catch (Throwable e){
                    log.error("dealTodo to deleteTodo error.", e);
                }
            }

            Map<String, String> nameMap = oACommonFieldManager.builderTitle(objectData, oaSyncApiEntity,dealTodoArg.getBizType(),dataCenterId);
            OASyncLogSnapshotDoc oaSyncLogEntity =
                    OASyncLogSnapshotDoc.builder().tenantId(tenantId).dataId(dealTodoArg.getSourceId()).businessDataId(objDataId).businessType(CrmTypeMessageEnum.CRM_TODO_TYPE.getType()).status(OASyncLogEnum.SYNC.getSyncType()).id(logId).
                            updateTime(new Date()).createTime(new Date()).objApiName(objectApiEnum.getObjApiName()).
                            eventType(OAEventEnum.DEAL.getEventStatus()).messageType(OAMessageTag.DEAL_TO_DO_TAG).message(i18NStringManager.getByEi(I18NStringEnum.s667,tenantId)).dataJson(dealTodoArgJson).title(nameMap.get("data_name"))
                            .dataName(nameMap.get("data_name")).dataCenterId(oaConnectInfoEntity.getId()).objectName(nameMap.get("object_name")).build();

            //插入mapping表
            OASyncLogMappingDoc mappingDoc = BeanUtil.copy(oaSyncLogEntity, OASyncLogMappingDoc.class);
            ObjectId mappingDocId=queryMappingIdByDoc(mappingDoc);
            oaSyncLogSnapshotDao.saveLog(tenantId,oaSyncLogEntity,mappingDocId,autoRetry);
            stopWatch.lap("recordLog");
            OAConnectParam oaConnectParam = GsonUtil.fromJson(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
            boolean finalDealToDelete = dealToDelete;
            try {
                List<String> recevierIds = dealTodoArg.getHandleUserIds().stream().map(String::valueOf).collect(Collectors.toList());
                List<OASyncLogMappingDoc> createDataReceiverCollection = oaSyncLogMappingsDao.findOneDataByReceiver(tenantId,oaConnectInfoEntity.getId(), objApiEnumByBizType.getObjApiName(), dealTodoArg.getSourceId(), recevierIds);
                if(CollectionUtils.isEmpty(createDataReceiverCollection)){
                    log.info("deal to do sleep");
                    //防止秒审批导致接口调用顺序混乱，外部接口先处理完deal，再处理create
                    Thread.sleep(3000);
                }
                stopWatch.lap("findMapping");
                // json字段转换
                List<OARequestModel> oaRequestModelList = oACommonFieldManager.exchangeOAField(tenantId, oaConnectInfoEntity.getId(),oaSyncApiEntity, describeResult.getData(),
                        finalDealToDelete ? dealTodoArg.getHandleUserIds() : dealTodoArg.getOperators() );
                stopWatch.lap("exchangeOaField");
                for (OARequestModel oaRequestModel : oaRequestModelList) {
                    Result<String> result = oaRequestManager.callCustomFuncAndRestfulService(tenantId,
                            oaConnectParam,
                            oaRequestModel,
                            oaSyncApiEntity.getUrl(),
                            oaSyncApiEntity.getRequestMode(),
                            dataCenterId,
                            oaSyncLogEntity);
                    stopWatch.lap("requestOa");
                    log.info("external oa {},result:{}",oaRequestModel,result);
                    ObjectId afterObjectId=ObjectId.get();
                    if (!result.isSuccess()) {
                        log.warn("dealTodo error,data={},result={}", new Gson().toJson(dealTodoArg), result);
                        String status=result.getErrCode().equals(ResultCodeEnum.OA_NOT_SYNC.getErrCode())?OASyncLogEnum.NOT_SYNC.getSyncType():OASyncLogEnum.FAIL.getSyncType();

                        OASyncLogSnapshotDoc failSyncLogEntity =
                                OASyncLogSnapshotDoc.builder().tenantId(tenantId).dataId(dealTodoArg.getSourceId()).businessDataId(objDataId).businessType(CrmTypeMessageEnum.CRM_TODO_TYPE.getType()).status(status).id(afterObjectId).
                                        updateTime(new Date()).createTime(new Date()).objApiName(objectApiEnum.getObjApiName()).receiverId(oaRequestModel.getReceiverId()).
                                        eventType(OAEventEnum.DEAL.getEventStatus()).messageType(OAMessageTag.DEAL_TO_DO_TAG).message(result.getErrMsg()).title(nameMap.get("data_name")).objectName(nameMap.get("object_name"))
                                        .dataName(nameMap.get("data_name")).dataCenterId(oaConnectInfoEntity.getId()).dataJson(oaRequestModel.getRequestJson()).build();
                        failSyncLogEntity.setUrl(oaSyncLogEntity.getUrl());
                        failSyncLogEntity.setAplApiName(oaSyncLogEntity.getAplApiName());
                        failSyncLogEntity.setMethod(oaSyncLogEntity.getMethod());
                        failSyncLogEntity.setHeader(oaSyncLogEntity.getHeader());
                        failSyncLogEntity.setBody(oaSyncLogEntity.getBody());
                        failSyncLogEntity.setResponse(oaSyncLogEntity.getResponse());
                        log.info("external oa {},result:{},insert",oaRequestModel,failSyncLogEntity);
                        mappingDoc.setStatus(OASyncLogEnum.FAIL.getSyncType());
                        oaSyncLogSnapshotDao.saveLog(tenantId,failSyncLogEntity,null,autoRetry);
                    } else {
                        OASyncLogSnapshotDoc successLogEntity =
                                OASyncLogSnapshotDoc.builder().tenantId(tenantId).dataId(dealTodoArg.getSourceId()).businessDataId(objDataId).businessType(CrmTypeMessageEnum.CRM_TODO_TYPE.getType()).status(OASyncLogEnum.SUCCESS.getSyncType()).id(afterObjectId).
                                        updateTime(new Date()).createTime(new Date()).objApiName(objectApiEnum.getObjApiName()).receiverId(oaRequestModel.getReceiverId()).
                                        eventType(OAEventEnum.DEAL.getEventStatus()).messageType(OAMessageTag.DEAL_TO_DO_TAG).message(result.getErrMsg()).title(nameMap.get("data_name")).objectName(nameMap.get("object_name"))
                                        .dataName(nameMap.get("data_name")).dataCenterId(oaConnectInfoEntity.getId()).dataJson(oaRequestModel.getRequestJson()).build();
                        successLogEntity.setUrl(oaSyncLogEntity.getUrl());
                        successLogEntity.setAplApiName(oaSyncLogEntity.getAplApiName());
                        successLogEntity.setMethod(oaSyncLogEntity.getMethod());
                        successLogEntity.setHeader(oaSyncLogEntity.getHeader());
                        successLogEntity.setBody(oaSyncLogEntity.getBody());
                        successLogEntity.setResponse(oaSyncLogEntity.getResponse());
                        log.info("external oa {},result:{},insert",oaRequestModel,successLogEntity);
                        mappingDoc.setStatus(OASyncLogEnum.SUCCESS.getSyncType());
                        oaSyncLogSnapshotDao.saveLog(tenantId,successLogEntity,null,autoRetry);
                    }
                }
                oaSyncLogSnapshotDao.deleteByObjectId(tenantId,logId);
                oaSyncLogMappingsDao.deleteByObjectId(tenantId,mappingDocId);
            } catch (Throwable e) {
                log.error("dealTodo error.", e);
                OASyncLogSnapshotDoc exceptionSyncLogEntity =
                        OASyncLogSnapshotDoc.builder().tenantId(tenantId).dataId(dealTodoArg.getSourceId()).businessDataId(objDataId).businessType(CrmTypeMessageEnum.CRM_TODO_TYPE.getType()).status(OASyncLogEnum.EXCEPTION.getSyncType()).id(logId).
                                updateTime(new Date()).createTime(new Date()).objApiName(objectApiEnum.getObjApiName()).
                                eventType(OAEventEnum.DEAL.getEventStatus()).messageType(OAMessageTag.DEAL_TO_DO_TAG).message(e.getMessage()).title(nameMap.get("data_name")).objectName(nameMap.get("object_name"))
                                .dataName(nameMap.get("data_name")).dataCenterId(oaConnectInfoEntity.getId()).dataJson(GsonUtil.toJson(dealTodoArg)).build();
                exceptionSyncLogEntity.setUrl(oaSyncLogEntity.getUrl());
                exceptionSyncLogEntity.setAplApiName(oaSyncLogEntity.getAplApiName());
                exceptionSyncLogEntity.setMethod(oaSyncLogEntity.getMethod());
                exceptionSyncLogEntity.setHeader(oaSyncLogEntity.getHeader());
                exceptionSyncLogEntity.setBody(oaSyncLogEntity.getBody());
                exceptionSyncLogEntity.setResponse(oaSyncLogEntity.getResponse());
                oaSyncLogSnapshotDao.updateByObjectId(tenantId,logId,exceptionSyncLogEntity);
                mappingDoc.setStatus(OASyncLogEnum.EXCEPTION.getSyncType());
                mappingDoc.setLastSyncLogId(logId);
                oaSyncLogMappingsDao.updateByObjectId(tenantId,mappingDocId,mappingDoc);
            }
        }
        stopWatch.lap("finish");
        stopWatch.log();
        return dealTodoResult;
    }

    public DeleteTodoResult deleteTodo(DeleteTodoArg deleteTodoArg,boolean autoRetry,String dataCenterId) {
        String deleteTodoArgJson = new Gson().toJson(deleteTodoArg);
        log.info("ExternalToDoManager deleteTodo json:{}", deleteTodoArgJson);
        DeleteTodoResult deleteTodoResult = new DeleteTodoResult();
        deleteTodoResult.setCode(200);
        String tenantId = String.valueOf(deleteTodoArg.getEi());
        ObjectApiEnum objectApiEnum = ObjectApiEnum.getObjApiEnumByBizType(deleteTodoArg.getBizType());
        if (objectApiEnum == null) {
            return deleteTodoResult;
        }
        List<OAConnectInfoEntity> oaConnectInfoEntities=null;
        if(StringUtils.isNotEmpty(dataCenterId)){
            OAConnectInfoEntity oaConnectInfoById = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getOAConnectInfoById(tenantId, dataCenterId);
            oaConnectInfoEntities=Lists.newArrayList(oaConnectInfoById);
        }else{
            oaConnectInfoEntities=oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
        }
        for (OAConnectInfoEntity oaConnectInfoEntity : oaConnectInfoEntities) {

            OASyncApiEntity oaSyncApiEntity = oaSyncApiDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId,oaConnectInfoEntity.getId(), OAEventEnum.DELETE.getEventStatus(),
                    objectApiEnum.getObjApiName());
            if (oaSyncApiEntity == null || oaConnectInfoEntity == null) {
                continue;
            }
            ObjectId logId = ObjectId.get();
             dataCenterId = oaConnectInfoEntity.getId();
            OASyncLogSnapshotDoc oaSyncLogEntity =
                    OASyncLogSnapshotDoc.builder().tenantId(tenantId).dataId(deleteTodoArg.getSourceId()).status(OASyncLogEnum.SYNC.getSyncType()).id(logId).
                            updateTime(new Date()).dataCenterId(oaConnectInfoEntity.getId()).createTime(new Date()).objApiName(objectApiEnum.getObjApiName()).dataName("").objectName("")
                            .eventType(OAEventEnum.DELETE.getEventStatus()).messageType(OAMessageTag.DELETE_TO_DO).message(i18NStringManager.getByEi(I18NStringEnum.s667,tenantId)).dataJson(deleteTodoArgJson).build();

            //插入mapping表
            OASyncLogMappingDoc mappingDoc = BeanUtil.copy(oaSyncLogEntity, OASyncLogMappingDoc.class);
            ObjectId mappingDocId=queryMappingIdByDoc(mappingDoc);
            oaSyncLogSnapshotDao.saveLog(tenantId,oaSyncLogEntity,mappingDocId,autoRetry);
            try {
//            // 休眠5s，防止秒审批导致接口调用顺序混乱，外部接口先处理完delete，再处理create
//            Thread.sleep(2000);
                //先保证数据在同步日志可见
                OAConnectParam oaConnectParam = GsonUtil.fromJson(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
                //！！！删除事件不能再反查对象数据，该接口查询不到已失效或删除的数据
                // 插入同步中日志

                String objDataId=deleteTodoArg.getExtraDataMap().get("objectId");//这里update的时候，消息平台不会透传objDataId.
                Result2<ControllerGetDescribeResult> controllerGetDescribeResultResult2 = approvalTaskManager.queryAllStatusBizDataByType(tenantId, dataCenterId,deleteTodoArg.getSourceId(),objDataId,deleteTodoArg.getBizType(), objectApiEnum.getObjApiName());
                ObjectData objectData = controllerGetDescribeResultResult2.getData().getData();
                objDataId=objectData.getId();
                objectData.put("sourceId",deleteTodoArg.getSourceId());
                Map<String, String> nameMap = oACommonFieldManager.builderTitle(objectData, oaSyncApiEntity,deleteTodoArg.getBizType(),dataCenterId);
                // json字段转换
                List<OARequestModel> oaRequestModelList = oACommonFieldManager.exchangeDeleteOAField(tenantId,dataCenterId, oaSyncApiEntity, controllerGetDescribeResultResult2.getData(),deleteTodoArg);
                for (OARequestModel oaRequestModel : oaRequestModelList) {
                    Result<String> result = oaRequestManager.callCustomFuncAndRestfulService(tenantId,
                            oaConnectParam,
                            oaRequestModel,
                            oaSyncApiEntity.getUrl(),
                            oaSyncApiEntity.getRequestMode(),
                            dataCenterId,oaSyncLogEntity);
                    ObjectId afterObjectId=ObjectId.get();
                    if (!result.isSuccess()) {
                        log.warn("deleteTodo error,data={},result={}", new Gson().toJson(deleteTodoArg), result);
                        String status=result.getErrCode().equals(ResultCodeEnum.OA_NOT_SYNC.getErrCode())?OASyncLogEnum.NOT_SYNC.getSyncType():OASyncLogEnum.FAIL.getSyncType();

                        OASyncLogSnapshotDoc failSyncLogEntity =
                                OASyncLogSnapshotDoc.builder().tenantId(tenantId).dataId(deleteTodoArg.getSourceId()).businessDataId(objDataId).businessType(CrmTypeMessageEnum.CRM_TODO_TYPE.getType()).status(status).id(afterObjectId).
                                        updateTime(new Date()).createTime(new Date()).objApiName(objectApiEnum.getObjApiName()).
                                        eventType(OAEventEnum.DELETE.getEventStatus()).message(result.getErrMsg()).dataJson(oaRequestModel.getRequestJson()).dataName(nameMap.get("data_name")).
                                        title(nameMap.get("data_name")).dataCenterId(oaConnectInfoEntity.getId()).objectName(nameMap.get("object_name")).receiverId(oaRequestModel.getReceiverId()).build();

                        failSyncLogEntity.setUrl(oaSyncLogEntity.getUrl());
                        failSyncLogEntity.setAplApiName(oaSyncLogEntity.getAplApiName());
                        failSyncLogEntity.setMethod(oaSyncLogEntity.getMethod());
                        failSyncLogEntity.setHeader(oaSyncLogEntity.getHeader());
                        failSyncLogEntity.setBody(oaSyncLogEntity.getBody());
                        failSyncLogEntity.setResponse(oaSyncLogEntity.getResponse());

                        mappingDoc.setStatus(OASyncLogEnum.FAIL.getSyncType());
                        oaSyncLogSnapshotDao.saveLog(tenantId,failSyncLogEntity,null,autoRetry);
                    } else {
                        OASyncLogSnapshotDoc successSyncLogEntity =
                                OASyncLogSnapshotDoc.builder().tenantId(tenantId).dataId(deleteTodoArg.getSourceId()).businessType(CrmTypeMessageEnum.CRM_TODO_TYPE.getType()).businessDataId(objDataId).status(OASyncLogEnum.SUCCESS.getSyncType()).id(afterObjectId).
                                        updateTime(new Date()).createTime(new Date()).objApiName(objectApiEnum.getObjApiName()).
                                        eventType(OAEventEnum.DELETE.getEventStatus()).dataCenterId(oaConnectInfoEntity.getId()).message(result.getErrMsg()).dataJson(oaRequestModel.getRequestJson()).receiverId(oaRequestModel.getReceiverId()).dataName(nameMap.get("data_name")).objectName(nameMap.get("object_name")).build();
                        successSyncLogEntity.setUrl(oaSyncLogEntity.getUrl());
                        successSyncLogEntity.setAplApiName(oaSyncLogEntity.getAplApiName());
                        successSyncLogEntity.setMethod(oaSyncLogEntity.getMethod());
                        successSyncLogEntity.setHeader(oaSyncLogEntity.getHeader());
                        successSyncLogEntity.setBody(oaSyncLogEntity.getBody());
                        successSyncLogEntity.setResponse(oaSyncLogEntity.getResponse());
                        oaSyncLogSnapshotDao.saveLog(tenantId,successSyncLogEntity,null,autoRetry);
                        mappingDoc.setStatus(OASyncLogEnum.SUCCESS.getSyncType());
                    }
                }
                oaSyncLogMappingsDao.deleteByObjectId(tenantId,mappingDocId);
                oaSyncLogSnapshotDao.deleteByObjectId(tenantId,logId);
            } catch (Throwable e) {
                log.error("deleteTodo error.", e);
                OASyncLogSnapshotDoc exceptionSyncLogEntity =
                        OASyncLogSnapshotDoc.builder().tenantId(tenantId).dataId(deleteTodoArg.getSourceId()).businessType(CrmTypeMessageEnum.CRM_TODO_TYPE.getType()).status(OASyncLogEnum.EXCEPTION.getSyncType()).id(logId).
                                updateTime(new Date()).createTime(new Date()).objApiName(objectApiEnum.getObjApiName()).
                                eventType(OAEventEnum.DELETE.getEventStatus()).dataCenterId(oaConnectInfoEntity.getId()).message(e.getMessage()).title("").dataJson(GsonUtil.toJson(deleteTodoArg)).build();
                exceptionSyncLogEntity.setUrl(oaSyncLogEntity.getUrl());
                exceptionSyncLogEntity.setAplApiName(oaSyncLogEntity.getAplApiName());
                exceptionSyncLogEntity.setMethod(oaSyncLogEntity.getMethod());
                exceptionSyncLogEntity.setHeader(oaSyncLogEntity.getHeader());
                exceptionSyncLogEntity.setBody(oaSyncLogEntity.getBody());
                exceptionSyncLogEntity.setResponse(oaSyncLogEntity.getResponse());
                oaSyncLogSnapshotDao.updateByObjectId(tenantId,logId,exceptionSyncLogEntity);
                mappingDoc.setStatus(OASyncLogEnum.EXCEPTION.getSyncType());
                mappingDoc.setLastSyncLogId(logId);
                oaSyncLogMappingsDao.updateByObjectId(tenantId,mappingDocId,mappingDoc);
            }

        }


        return deleteTodoResult;
    }

    //当数据是同步中，或者是异常重试的时候，会重新走一遍上面的对应类型，导致后续delete的mappingId的时候是错误的。需要查询下mapping是否已经存在该代办的mappingId
    public ObjectId queryMappingIdByDoc(OASyncLogMappingDoc oaSyncLogMappingDoc){
        QueryOASyncLogArg queryOASyncLogArg=new QueryOASyncLogArg();
        queryOASyncLogArg.setEventType(oaSyncLogMappingDoc.getEventType());
        queryOASyncLogArg.setTenantId(oaSyncLogMappingDoc.getTenantId());
        queryOASyncLogArg.setDataId(oaSyncLogMappingDoc.getDataId());
        queryOASyncLogArg.setReceiverId(oaSyncLogMappingDoc.getReceiverId());
        queryOASyncLogArg.setStatus(oaSyncLogMappingDoc.getStatus());
        queryOASyncLogArg.setObjApiName(oaSyncLogMappingDoc.getObjApiName());
        queryOASyncLogArg.setDataCenterId(oaSyncLogMappingDoc.getDataCenterId());
        ArrayList<String> statusValues = Lists.newArrayList(OASyncLogEnum.SYNC.getSyncType(), OASyncLogEnum.EXCEPTION.getSyncType());
        List<OASyncLogMappingDoc> oaSyncLogMappingDocs = oaSyncLogMappingsDao.pageByFiltersByFilterInStatus(queryOASyncLogArg,statusValues);
        if(CollectionUtils.isNotEmpty(oaSyncLogMappingDocs)){
            return oaSyncLogMappingDocs.get(0).getId();
        }
        return ObjectId.get();
    }





}
