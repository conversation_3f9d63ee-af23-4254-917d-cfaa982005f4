package com.fxiaoke.open.oasyncdata.interceptor;


import com.alibaba.fastjson.JSON;
import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.enums.ValidateStatus;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.fxiaoke.open.oasyncdata.db.dao.ErpConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.model.UserVo;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Data
@Slf4j
public class UserInterceptors extends HandlerInterceptorAdapter {

    @Autowired
    private ActiveSessionAuthorizeService activeSessionAuthorizeService;
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (request.getRequestURI().contains("/cep")){
            //cep解析身份
            UserVo userVo = new UserVo();
            userVo.setEnterpriseAccount(request.getHeader("X-fs-Enterprise-Account"));
            userVo.setEnterpriseId(Integer.valueOf(request.getHeader("X-fs-Enterprise-Id")));
            String employeeId = request.getHeader("X-fs-Employee-Id");
            if (Objects.nonNull(employeeId)) {
                userVo.setEmployeeId(Integer.valueOf(employeeId));
            }else {
                userVo.setEmployeeId(-10000);
            }
            UserContextHolder.get().set(userVo);
            if(ObjectUtils.isEmpty(userVo.getDataCenterId())){
                //不传数据中心id,默认一个数据
                String tenantId=String.valueOf(userVo.getEnterpriseId());

                List<OAConnectInfoEntity> byTenantId = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
                if(CollectionUtils.isNotEmpty(byTenantId)){
                    userVo.setDataCenterId(byTenantId.get(0).getId());
                }
            }
            //参数统一处理
            return true;
        }
        String fsAuthXcookie = getFsAuthXCookie(request.getCookies());
        log.info("get cookie = {}", fsAuthXcookie);
        if (fsAuthXcookie != null) {
            CookieToAuth.Argument argument = new CookieToAuth.Argument();
            argument.setCookie(fsAuthXcookie);
            argument.setFsToken(null);
            argument.setIp(null);
            CookieToAuth.Result<AuthXC> cookieToAuthResult = activeSessionAuthorizeService.cookieToAuthXC(argument);
            if (!cookieToAuthResult.isSucceed()
                    || !ValidateStatus.NORMAL.equals(cookieToAuthResult.getValidateStatus())) {
                log.warn("activeSessionAuthorizeService.cookieToAuthXC failed, argument={}, resultUser=[{}]", argument, cookieToAuthResult);
                return responseNoUserResult(request, response);
            }
            AuthXC authXC = cookieToAuthResult.getBody();
            log.info("authXC={}", authXC);

            if (StringUtils.isBlank(authXC.getEnterpriseAccount())) {
                return responseNoUserResult(request, response);
            }
            if (authXC.getEmployeeId() == null || authXC.getEmployeeId() < 0) {
                return responseNoUserResult(request, response);
            }
            if (request.getRequestURI().contains("authToolsUpdate")) {
                //校验超级管理员身份
                boolean adminUser = checkSuperAdminUser(authXC);
                if (adminUser==false) {
                    return responseNoUserResult(request, response);
                }
            }
            UserContextHolder.get().set(new UserVo(authXC));
            fillDcId(request);
            return checkAuth(request, response, authXC);
        }
        return responseNoUserResult(request, response);
    }

    private boolean checkSuperAdminUser(AuthXC authXC){
        //通过fsCookie获取的身份验证
        String fullUser = authXC.getEnterpriseAccount() + "." + authXC.getEmployeeId();
        if(ConfigCenter.SUPER_ADMINS.contains(fullUser)){
            return true;
        }
        return false;
    }

    private boolean responseWrongEnvironment(HttpServletRequest request, HttpServletResponse response) {
        response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        Result<Void> result = new Result<>(ResultCodeEnum.ERROR_ENVIRONMENT);
        PrintWriter writer;
        try {
            writer = response.getWriter();
        } catch (IOException e) {
            log.error("httpServletResponse error. uri[{}]", request.getRequestURI(), e);
            return false;
        }
        writer.append(JSON.toJSONString(result));
        writer.flush();
        writer.close();
        return false;
    }

    /**
     * 装填数据中心id
     *
     * @param request
     */
    private void fillDcId(HttpServletRequest request) {
        String header = request.getHeader("esd-dcid");
        UserContextHolder.get().get().setDataCenterId(header);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        //防止内存泄露
        UserContextHolder.remove();
    }


    private boolean checkAuth(HttpServletRequest request, HttpServletResponse response, AuthXC authXC) {
        String uri = request.getRequestURI();
        if (uri.startsWith("/erp/syncdata/superadmin")) {
            String fullUser = authXC.getEnterpriseAccount() + "." + authXC.getEmployeeId();
            if (ConfigCenter.SUPER_ADMINS.contains(fullUser)) {
                return true;
            }
            return responseNoUserResult(request, response);
        }
        return true;
    }

    /**
     * 获取用户信息失败时返回json
     *
     * @param request  request
     * @param response response
     */
    private boolean responseNoUserResult(HttpServletRequest request, HttpServletResponse response) {
        if (notNeedUser(request.getRequestURI())) {
            return true;
        }
        response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        Result<Void> result = new Result<>(ResultCodeEnum.NO_USER);
        PrintWriter writer;
        try {
            writer = response.getWriter();
        } catch (IOException e) {
            log.error("httpServletResponse error. uri[{}]", request.getRequestURI(), e);
            return false;
        }
        writer.append(JSON.toJSONString(result));
        writer.flush();
        writer.close();
        return false;
    }

    /**
     * 是否不需要登录身份
     *
     * @param uri
     * @return
     */
    private boolean notNeedUser(String uri) {
        log.info("uri={}", uri);
        return uri.startsWith("/erp/syncdata/noAuth")
                || uri.contains("/cep")
                || uri.contains("/swagger")
                || uri.contains("/inner")
                || uri.contains("/erp/syncdata/open/oa")
                || uri.contains("/erp/syncdata/oa/tools")
                ||uri.contains("/erp/syncdata/open/saml")
                || uri.contains("/erp/syncdata/out");
    }

    /**
     * cookies中获取FsAuthX
     *
     * @param cookies
     * @return
     */
    private String getFsAuthXCookie(Cookie[] cookies) {
        if (cookies == null) {
            return null;
        } else {
            for (Cookie cookie : cookies) {
                if ("FSAuthXC".equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
            return null;
        }
    }
}
