package com.fxiaoke.open.erpsyncdata.main.dispatcher.processor;

import com.fxiaoke.dispatcher.common.FieldOrder;
import com.fxiaoke.dispatcher.listener.finder.IFinder;
import com.google.common.collect.ImmutableList;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 */
public class OrderDtimeFinder implements IFinder {
    @Override
    public List<FieldOrder> orders() {
        return ImmutableList.of(FieldOrder.of("order", true), FieldOrder.of("dtime", true));
    }
}
