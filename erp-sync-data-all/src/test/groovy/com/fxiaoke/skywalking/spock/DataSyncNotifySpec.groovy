package com.fxiaoke.skywalking.spock

import com.alibaba.fastjson.JSONObject
import com.facishare.qixin.api.model.message.content.TextInfo
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum
import com.fxiaoke.open.erpsyncdata.common.rule.ConditionUtil
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataSyncNotifyManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldMappingManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.FieldDbManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LocalDispatcherUtil
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SimpleSyncDataArg
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncStatusMessageArg
import com.fxiaoke.paasauthrestapi.common.result.Result
import com.fxiaoke.paasauthrestapi.result.RoleUserResult
import com.fxiaoke.paasauthrestapi.service.PaasAuthService
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.googlecode.aviator.AviatorEvaluator
import org.apache.commons.lang3.tuple.MutableTriple
import org.bson.Document
import org.bson.types.ObjectId
import org.junit.Ignore
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.TimeUnit
import java.util.function.BiConsumer
import java.util.stream.Collectors

/**
 * <AUTHOR> @Date 2022/11/14 17:37
 * @Version 1.0
 */
@Ignore
class DataSyncNotifySpec extends  Specification{
    def syncPloyDetailSnapshotManager=Mock(SyncPloyDetailSnapshotManager)
    def fieldDbManager=Mock(FieldDbManager)
    def erpTempDataDao=Mock(ErpTempDataDao)
    def paasAuthService=Mock(PaasAuthService)
    def objectDataServiceV3=Mock(ObjectDataServiceV3)
    def erpFieldMappingManager=Mock(ErpFieldMappingManager)
    def localDispatcherUtil = new LocalDispatcherUtil<>({ key, list ->

        //数据结构由一个tenant_ploy的企业集成集合与以企业集成集合的value做为key,记录一段时间内同步过的数据id+score(time)
        SyncStatusMessageArg syncStatusMessageArg = generateTenantIdInfo(key);
        sendSyncMessageToUser(list, syncStatusMessageArg);
        localDispatcherUtil.setBatchProcessTimeLimitInSecond(ConfigCenter.NOTIFY_DELAY_TIME_SECONDS);
    } as BiConsumer);

    def dataSyncNotifyManager=new DataSyncNotifyManager(syncPloyDetailSnapshotManager: syncPloyDetailSnapshotManager,fieldDbManager: fieldDbManager,
            erpTempDataDao: erpTempDataDao,paasAuthService: paasAuthService,objectDataServiceV3: objectDataServiceV3,erpFieldMappingManager: erpFieldMappingManager,localDispatcherUtil:localDispatcherUtil);

    private SyncStatusMessageArg generateTenantIdInfo(String tenantApiName){
        //SYNC_DATA_NOTIFY_企业id_sourceApiName_destApiName
        log.info("generateTenantIdInfo key:{}",tenantApiName);
        MutableTriple<String,String,String> triple=JSONObject.parseObject(tenantApiName,MutableTriple.class);
        String tenantId=triple.getLeft();
        String sourceApiName=triple.getMiddle();
        String destApiName=triple.getRight();
        SyncPloyDetailEntity entityByTenantIdAndObjApiName = syncPloyDetailManager.getEntityByTenantIdAndObjApiName(tenantId, sourceApiName, destApiName);
        SyncStatusMessageArg syncStatusMessageArg=new SyncStatusMessageArg();
        syncStatusMessageArg.setTenantId(tenantId);
        syncStatusMessageArg.setPloyDetailName(entityByTenantIdAndObjApiName.getIntegrationStreamName());
        syncStatusMessageArg.setEndTime(System.currentTimeMillis());
        //减掉聚合时间
        syncStatusMessageArg.setStartTime(System.currentTimeMillis()- TimeUnit.MINUTES.toMillis(5));
        String direction=entityByTenantIdAndObjApiName.getSourceTenantType().equals(TenantTypeEnum.CRM.getType())?"CRM->ERP":"ERP->CRM";
        syncStatusMessageArg.setSyncDirection(direction);
        return syncStatusMessageArg;

    }

    public void sendSyncMessageToUser(List<SimpleSyncDataArg> syncDataString,SyncStatusMessageArg syncStatusMessageArg){
        //聚合人员数据
        log.info("syncDataString:{}",syncDataString);
        Map<Integer, List<SimpleSyncDataArg>> receiverUserMap = syncDataString.stream().collect(Collectors.groupingBy(SimpleSyncDataArg.&getReceivers));
        //组装发布消息
        for (Integer received : receiverUserMap.keySet()) {
            List<SimpleSyncDataArg> simpleSyncDataArgs = receiverUserMap.get(received);
            Map<Integer, List<SyncStatusMessageArg.SyncDataStatusMessage>> statusAllList = simpleSyncDataArgs.stream().map({ item ->
                SyncStatusMessageArg.SyncDataStatusMessage syncDataStatusMessage = new SyncStatusMessageArg.SyncDataStatusMessage();
                syncDataStatusMessage.setDataId(item.getSourceDataId());
                syncDataStatusMessage.setStatus(SyncDataStatusEnum.isSyncDataStatusReturnInt(item.getStatus()));
                syncDataStatusMessage.setStatusMessage(SyncStatusEnum.getNameByStatus(SyncDataStatusEnum.isSyncDataStatusReturnInt(item.getStatus())));
                syncDataStatusMessage.setRemark(item.getRemark());
                return syncDataStatusMessage;
            }).collect(Collectors.groupingBy({ item -> item.getStatus() }));

            syncStatusMessageArg.setSuccessList(statusAllList.get(SyncStatusEnum.SUCCESS.getStatus()));
            syncStatusMessageArg.setErrorList(statusAllList.get(SyncStatusEnum.FAILED.getStatus()));
//            com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<List<TextInfo>> messageResult = notificationService.generateDataSyncResult(syncStatusMessageArg);
//            notificationService.sendErpSyncDataAppMultiNotice(messageResult.getData(),syncStatusMessageArg.getTenantId(),Lists.newArrayList(received));
        }

    }


    private List<SimpleSyncDataArg> createRedisData(SyncDataEntity syncDataEntity, Set<Integer> empSet){
        log.info("createRedis data:{}.{}",syncDataEntity,JSONObject.toJSONString(empSet));
        List<SimpleSyncDataArg> pushDataLists=Lists.newArrayList();
        // 根据集成流条件拆分接收者，存储在redis里面的是按接收人拆分后的信息。
        for (Integer receiver : empSet) {
            SimpleSyncDataArg dataArg = BeanUtil.copy(syncDataEntity, SimpleSyncDataArg.class);
            dataArg.setReceivers(receiver);
            pushDataLists.add(dataArg);
        }
        return pushDataLists;
    }

    @Unroll
    def "校验数据符合条件的通知"(){
        given:
        def tenantId=tenantIdValue;
        def syncDataEntityMap=getEntity;

         "mock getEntryBySnapshotId返回的结果"
        syncPloyDetailSnapshotManager.getEntryBySnapshotId(_ as String,_ as String) >> entryBySnapshotEntity
         "mock getRelation返回的结果"
        fieldDbManager.getRelation(_ as String, _ as String) >> erpObjectRelationshipEntity
         "mock getRelation返回的结果"
        fieldDbManager.convertIdArg2Erp(_ as ErpIdArg) >> erpIdArg
        //        fieldDbManager.convertIdArg2Erp(_ as ErpIdArg) >> {args ->u
//          def  erpIdArg = args[0]
//
//        }
         "mock hasUsedIdQuery接口"
        fieldDbManager.hasUsedIdQuery(_ as ErpIdArg,null) >> hasUsedIdQuery
         "erpTempDataDao.getErpObjDataById"
        erpTempDataDao.getErpObjDataById(_ as String) >> erpObjDataById
         "erpTempDataDao.getErpObjDataByNum"
        erpTempDataDao.getErpObjDataByNum(_ as String) >> erpObjDataByNumber
         "paasAuthService"
        paasAuthService.roleUser() >> roleUserRes
        when :
        dataSyncNotifyManager.pushDataToNotify(tenantId,syncDataEntityMap)
        then:
        localDispatcherUtil.delayQueue.size()==2
        where:
        tenantIdValue | getEntity       | entryBySnapshotEntity       | erpObjectRelationshipEntity | hasUsedIdQuery    | erpIdArg            | erpObjDataById          | erpObjDataByNumber | roleUserRes
//        "82777"  | getEntityMap("1001") | getEntryBySnapshotEntity("1001") |new ErpObjectRelationshipEntity()| true | convertErpIdArg() | getErpObjDataById() | getErpObjDataByNumber() | new Result<RoleUserResult>()
        "82777"  | getEntityMap("1002") | getEntryBySnapshotEntity("1001") |getRelation()| true | convertErpIdArg() | getErpObjDataById() | getErpObjDataByNumber() | new Result<RoleUserResult>()

    }

    def getEntityMap(String id){
        String dataJson="";
        if(id.equals("1001")){
            dataJson="{\"createTime\":1675131468680,\"destData\":{\"object_describe_api_name\":\"ProductObj\",\"tenant_id\":\"82777\",\"record_type\":\"default__c\",\"owner\":[\"1000\"],\"product_status\":\"1\",\"price\":\"20\",\"is_saleable\":true,\"batch_sn\":\"1\",\"name\":\"CH2037#拨测测试产品20230130-51\",\"category\":\"60\",\"is_package\":false,\"product_code\":\"CH2037\",\"_id\":\"63d78fa890232f0001df225b\"},\"destDataId\":\"63d78fa890232f0001df225b\",\"destEventType\":2,\"destObjectApiName\":\"ProductObj\",\"destTenantId\":\"82777\",\"destTenantType\":1,\"id\":\"63d87a4c3c69c51b0176624d\",\"isDeleted\":false,\"operatorId\":\"-10000\",\"remark\":\"success\",\"sourceData\":{\"tenant_id\":\"82777\",\"SubHeadEntity.FSuite\":\"0\",\"FDescription\":\" \",\"FMaterialGroup.FNumber\":\"60\",\"FSpecification\":\" \",\"Number\":\"CH2037\",\"object_describe_api_name\":\"BD_MATERIAL.BillHead\",\"VirtualHasBatchAndSerial\":\"1\",\"SubHeadEntity.FBaseUnitId.FNumber\":\"Pcs\",\"name\":\"拨测测试产品20230130-51\",\"comName\":\"CH2037#拨测测试产品20230130-51\",\"_id\":\"CH2037\"},\"sourceDataId\":\"CH2037\",\"sourceDetailSyncDataIds\":{},\"sourceEventType\":2,\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"sourceTenantId\":\"82777\",\"sourceTenantType\":2,\"status\":6,\"syncLogId\":\"J-E.82777.0.BD_MATERIAL.1VGJNM0KQZq.0.0\",\"syncPloyDetailSnapshotId\":\"4ec9604fc5234fe091a833485080b666\",\"tenantId\":\"82777\",\"updateTime\":1675131468680}";

        }else if(id.equals("1002")){
            //有历史任务的id
            dataJson="{\n" +
                    "    \"createTime\":1675131468680,\n" +
                    "    \"destData\":{\n" +
                    "        \"object_describe_api_name\":\"ProductObj\",\n" +
                    "        \"tenant_id\":\"82777\",\n" +
                    "        \"record_type\":\"default__c\",\n" +
                    "        \"owner\":[\n" +
                    "            \"1000\"\n" +
                    "        ],\n" +
                    "        \"product_status\":\"1\",\n" +
                    "        \"price\":\"20\",\n" +
                    "        \"is_saleable\":true,\n" +
                    "        \"batch_sn\":\"1\",\n" +
                    "        \"name\":\"1941自动化产品20221231-74\",\n" +
                    "        \"category\":\"60\",\n" +
                    "        \"is_package\":false,\n" +
                    "        \"product_code\":\"CH1951\",\n" +
                    "        \"_id\":\"63d78fa890232f0001df225b\"\n" +
                    "    },\n" +
                    "    \"destDataId\":\"63d78fa890232f0001df225b\",\n" +
                    "    \"destEventType\":2,\n" +
                    "    \"destObjectApiName\":\"ProductObj\",\n" +
                    "    \"destTenantId\":\"82777\",\n" +
                    "    \"destTenantType\":1,\n" +
                    "    \"id\":\"63d87a4c3c69c51b0176624d\",\n" +
                    "    \"isDeleted\":false,\n" +
                    "    \"operatorId\":\"-10000\",\n" +
                    "    \"remark\":\"success\",\n" +
                    "    \"sourceData\":{\n" +
                    "        \"tenant_id\":\"82777\",\n" +
                    "        \"SubHeadEntity.FSuite\":\"0\",\n" +
                    "        \"FDescription\":\" \",\n" +
                    "        \"FMaterialGroup.FNumber\":\"60\",\n" +
                    "        \"FSpecification\":\" \",\n" +
                    "        \"Number\":\"CH1951\",\n" +
                    "        \"object_describe_api_name\":\"BD_MATERIAL.BillHead\",\n" +
                    "        \"VirtualHasBatchAndSerial\":\"1\",\n" +
                    "        \"SubHeadEntity.FBaseUnitId.FNumber\":\"Pcs\",\n" +
                    "        \"name\":\"拨测测试产品20230130-51\",\n" +
                    "        \"comName\":\"CH2037#拨测测试产品20230130-51\",\n" +
                    "        \"_id\":\"CH1951\"\n" +
                    "    },\n" +
                    "    \"sourceDataId\":\"CH1951\",\n" +
                    "    \"sourceDetailSyncDataIds\":{\n" +
                    "\n" +
                    "    },\n" +
                    "    \"sourceEventType\":2,\n" +
                    "    \"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\n" +
                    "    \"sourceTenantId\":\"82777\",\n" +
                    "    \"sourceTenantType\":2,\n" +
                    "    \"status\":6,\n" +
                    "    \"syncLogId\":\"J-E.82777.0.BD_MATERIAL.1VGJNM0KQZq.0.0\",\n" +
                    "    \"syncPloyDetailSnapshotId\":\"4ec9604fc5234fe091a833485080b666\",\n" +
                    "    \"tenantId\":\"82777\",\n" +
                    "    \"updateTime\":1675131468680\n" +
                    "}";
        }
        SyncDataEntity syncDataEntity= JSONObject.parseObject(dataJson,SyncDataEntity.class);
        Map<String, SyncDataEntity> entityMap=Maps.newHashMap();
        entityMap.put(id,syncDataEntity)

        return  entityMap;
    }

    def getEntryBySnapshotEntity(String entryId){
        def json="{\"continueNotify\":true,\"createTime\":1673503631766,\"destObjectApiName\":\"ProductObj\",\"destTenantId\":\"82777\",\"detailObjectSyncConditionsExpressions\":{},\"id\":\"4ec9604fc5234fe091a833485080b666\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"sourceTenantId\":\"82777\",\"status\":2,\"syncConditionsExpression\":\"true\",\"syncPloyData\":{\"detailObjectApiNames\":[]},\"syncPloyDetailData\":{\"destDataCenterId\":\"756743122421678080\",\"destObjectApiName\":\"ProductObj\",\"destTenantIds\":[\"82777\"],\"destTenantType\":1,\"detailObjectMappings\":[],\"detailObjectSyncConditions\":[],\"fieldMappings\":[{\"destApiName\":\"record_type\",\"destType\":\"record_type\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"default__c\",\"valueType\":1},{\"destApiName\":\"owner\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1000\",\"valueType\":1},{\"destApiName\":\"product_status\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"price\",\"destType\":\"currency\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"20\",\"valueType\":1},{\"destApiName\":\"is_saleable\",\"destType\":\"true_or_false\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"true\",\"valueType\":1},{\"destApiName\":\"batch_sn\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"1\",\"sourceOption\":\"1\"},{\"destOption\":\"3\",\"sourceOption\":\"3\"},{\"destOption\":\"2\",\"sourceOption\":\"2\"}],\"sourceApiName\":\"VirtualHasBatchAndSerial\",\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":1},{\"destApiName\":\"name\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"comName\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"category\",\"destType\":\"category\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialGroup.FNumber\",\"sourceType\":\"category\",\"value\":\"\"},{\"destApiName\":\"is_package\",\"destType\":\"true_or_false\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":false,\"sourceOption\":\"0\"},{\"destOption\":true,\"sourceOption\":\"1\"}],\"sourceApiName\":\"SubHeadEntity.FSuite\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"product_code\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"Number\",\"sourceType\":\"id\",\"value\":\"\"},{\"destApiName\":\"product_spec\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSpecification\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"unit\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"1\",\"sourceOption\":\"002\"},{\"destOption\":\"3\",\"sourceOption\":\"WJ001\"},{\"destOption\":\"10\",\"sourceOption\":\"UOM015\"},{\"destOption\":\"7\",\"sourceOption\":\"100011\"}],\"sourceApiName\":\"SubHeadEntity.FBaseUnitId.FNumber\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"barcode\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SubHeadEntity.FBARCODE\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDescription\",\"sourceType\":\"select_one\",\"value\":\"\"}],\"id\":\"59999545fdb347e39bf8f76ae80fddbd\",\"integrationStreamNodes\":{\"notifyComplementNode\":{\"dataRelatedOwner\":[],\"notifyConditionAviator\":\"((string.contains(remark,\\\"success\\\")))\",\"notifyConditionFilters\":[{\"fieldApiName\":\"remark\",\"fieldValue\":\"success\",\"operator\":\"LIKE\"}],\"notifyEmployees\":[1007],\"notifyRoles\":[],\"notifyStatus\":[2,1]}},\"sourceDataCenterId\":\"696453487420604416\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"sourceTenantIds\":[\"82777\"],\"sourceTenantType\":2,\"status\":1,\"syncConditions\":{\"apiName\":\"BD_MATERIAL.BillHead\",\"filters\":[],\"isSyncForce\":true},\"syncPloyId\":\"797fbe55f1394f07980dd6481a17b027\",\"syncRules\":{\"events\":[1,2,5,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncDependForce\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},\"syncPloyDetailId\":\"59999545fdb347e39bf8f76ae80fddbd\",\"syncPloyId\":\"797fbe55f1394f07980dd6481a17b027\",\"updateTime\":1673503631766}";
        SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity= JSONObject.parseObject(json,SyncPloyDetailSnapshotEntity.class)
        return  syncPloyDetailSnapshotEntity;
    }

    def getRelation(){
        ErpObjectRelationshipEntity erpObjectRelationshipEntity= JSONObject.toJSONString("{\"channel\":\"ERP_K3CLOUD\",\"createTime\":1627629714014,\"dataCenterId\":\"696453487420604416\",\"erpRealObjectApiname\":\"BD_MATERIAL\",\"erpSplitObjectApiname\":\"BD_MATERIAL.BillHead\",\"id\":\"696453488964108288\",\"splitSeq\":1,\"splitType\":\"NOT_SPLIT\",\"tenantId\":\"82777\",\"updateTime\":1627629714014}",
        ErpObjectRelationshipEntity.class);
        return erpObjectRelationshipEntity;
    }
    def getErpObjDataById(){
        return new Document();
    }
    def getErpObjDataByNumber(){
        def json="{\"_id\":{\"counter\":6556465,\"date\":1672419243000,\"machineIdentifier\":13837404,\"processIdentifier\":-9263,\"time\":1672419243000,\"timeSecond\":1672419243,\"timestamp\":1672419243},\"data_number\":\"CH1951\",\"dc_id\":\"696453487420604416\",\"obj_api_name\":\"BD_MATERIAL\",\"tenant_id\":\"82777\",\"update_time\":1675408784870,\"data_id\":\"814070\",\"remark\":\"数据触发成功\",\"trace_id\":\"J-E.82777.0.BD_MATERIAL.1VM44TPQ8Ok\",\"expire_time\":1675408763983,\"sync_log_id\":\"J-E.82777.0.BD_MATERIAL.1VM44TPQ8Ok.1\",\"create_time\":1672419243898,\"task_num\":[\"ploy_auto_query\",\"task_1675408710432\"],\"data_body\":\"{\\\"objAPIName\\\":null,\\\"masterFieldVal\\\":{\\\"erp_id\\\":\\\"814070\\\",\\\"erp_num\\\":\\\"CH1951\\\",\\\"FDescription\\\":\\\" \\\",\\\"SubHeadEntity.FBARCODE\\\":null,\\\"FMaterialGroup.FNumber\\\":\\\"F00001\\\",\\\"SubHeadEntity.FBaseUnitId.FNumber\\\":\\\"Pcs\\\",\\\"FSpecification\\\":\\\" \\\",\\\"Number\\\":\\\"CH1951\\\",\\\"SubHeadEntity.FSuite\\\":\\\"0\\\",\\\"name\\\":\\\"1941自动化产品20221231-74-edit\\\",\\\"comName\\\":\\\"CH1951#1941自动化产品20221231-74-edit\\\",\\\"VirtualHasBatchAndSerial\\\":\\\"1\\\"},\\\"detailFieldVals\\\":{},\\\"syncLogId\\\":\\\"J-E.82777.0.BD_MATERIAL.1VM44TPQ8Ok.1\\\"}\",\"status\":10020,\"last_sync_time\":1675408763983,\"data_md5\":\"8ba82b1822cd28737878bf500d63a517\",\"new_last_sync_time\":1675408763975,\"sync_status\":{\"59999545fdb347e39bf8f76ae80fddbd\":10060},\"need_query_status\":true}";
        Document document=JSONObject.parseObject(json,Document.class)
        return document;
    }

    def convertErpIdArg(){
        def json="{\"dataId\":\"CH1951\",\"dataIdIsNumber\":false,\"includeDetail\":true,\"objAPIName\":\"BD_MATERIAL.BillHead\",\"tenantId\":\"82777\"}";
        ErpIdArg erpIdArg=JSONObject.parseObject(json,ErpIdArg.class);

        return erpIdArg;
    }




    @Unroll
    def "校验avaiator表达式 #uid"(){
        given:
        def conditionExpress="FDescription != nil  && FDescription != ''";
        def envMap= valueMap
        when:
        boolean compiledExp=  AviatorEvaluator.execute(conditionExpress, envMap, false);
        System.out.println(compiledExp);
        // Execute with injected variables.
        then:
        with(compiledExp){
            compiledExp==type;
        }
        where: "表格方式验证订单信息的分支场景"
        uid | valueMap                 || type
        1   | ["FDescription": ''] || true

    }

    def "校验avaiator表达式2"(){
        given:
        def conditionExpress="(field_9m25X__c != nil  && field_9m25X__c != '')";
        def envMap= Maps.newHashMap();
        envMap.put("field_9m25X__c","3")
        when:
        boolean compiledExp=  AviatorEvaluator.execute(conditionExpress, envMap, false);
        System.out.println(compiledExp);
        // Execute with injected variables.
        then:
        with(compiledExp){
            compiledExp==true;
        }

    }







}
