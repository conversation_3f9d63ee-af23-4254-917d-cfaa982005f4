package com.fxiaoke.open.erpsyncdata.preprocess.manager

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> @Date: 17:19 2023/5/16
 * @Desc:
 */
class SpecialFieldPreprocessManagerTest extends Specification {
    def "ConvertFieldValueId"() {
        SpecialFieldPreprocessManager specialFieldPreprocessManager = new SpecialFieldPreprocessManager();
        ErpConnectInfoEntity connectInfo = new ErpConnectInfoEntity()
        connectInfo.setChannel(ErpChannelEnum.valueOf(channel))
        ObjectData objectData = new ObjectData();
        objectData.put(fieldKey, oldValue)
        objectData.putId(oldValue)
        objectData.put("name", name)
        ErpObjectFieldEntity fieldEntity = new ErpObjectFieldEntity()
        fieldEntity.setFieldExtendValue(fieldExtend)
        when:
        specialFieldPreprocessManager.convertFieldValueId(connectInfo, oldValue, fieldKey, objectData, crm2Erp, fieldEntity)
        then:
        objectData.get(fieldKey) == result && objectData.getId() == result
        where:
        channel       | oldValue | fieldKey    | crm2Erp | fieldExtend                                       | name    | result
        "ERP_K3CLOUD" | "123"    | "fieldKey1" | false   | "{\"splicing_formula\":\"1234+\$name\$+41234\"}"  | "name"  | "1234name41234"
        "ERP_K3CLOUD" | "123"    | "fieldKey2" | false   | "{\"splicing_formula\":\"1234+\$name\$+41234\"}"  | "name1" | "1234name141234"
        "ERP_K3CLOUD" | "123"    | "fieldKey3" | false   | "{\"splicing_formula\":\"1234+\$Fname\$+41234\"}" | "name"  | "123441234"
        "ERP_K3CLOUD" | "123"    | "fieldKey3" | false   | "{}"                                              | "name"  | "123"
        "ERP_K3CLOUD" | "123"    | "fieldKey3" | false   | "{\"splicing_formula\":\"\"}"                     | "name"  | "123"
    }

    def "ConvertFieldValueText"() {
        SpecialFieldPreprocessManager specialFieldPreprocessManager = new SpecialFieldPreprocessManager();
        ErpConnectInfoEntity connectInfo = new ErpConnectInfoEntity()
        connectInfo.setChannel(ErpChannelEnum.valueOf(channel))
        ObjectData objectData = new ObjectData();
        objectData.put("number", number)
        objectData.put(fieldKey, oldValue)
        ErpObjectFieldEntity fieldEntity = new ErpObjectFieldEntity()
        fieldEntity.setFieldExtendValue(fieldExtend)
        when:
        specialFieldPreprocessManager.convertFieldValueText(connectInfo, oldValue, fieldKey, objectData, crm2Erp, fieldEntity)
        then:
        objectData.get(fieldKey) == result && objectData.getName() == name
        where:
        channel       | oldValue | fieldKey    | crm2Erp | fieldExtend                                                                   | number  | result           | name
        "ERP_K3CLOUD" | "123"    | "fieldKey1" | false   | "{\"splicing_formula\":\"1234+\$number\$+41234\",\"isMainAttribute\": true}"  | "name"  | "1234name41234"  | "1234name41234"
        "ERP_K3CLOUD" | "123"    | "fieldKey2" | false   | "{\"splicing_formula\":\"1234+\$number\$+41234\",\"isMainAttribute\": true}"  | "name1" | "1234name141234" | "1234name141234"
        "ERP_K3CLOUD" | "123"    | "fieldKey3" | false   | "{\"splicing_formula\":\"1234+\$Fnumber\$+41234\",\"isMainAttribute\": true}" | "name"  | "123441234"      | "123441234"
        "ERP_K3CLOUD" | "123"    | "fieldKey1" | false   | "{\"isMainAttribute\": true}"                                                 | "name"  | "123"            | "123"
        "ERP_K3CLOUD" | "123"    | "fieldKey2" | false   | "{\"splicing_formula\":\"1234+\$number\$+41234\",\"isMainAttribute\": false}" | "name1" | "1234name141234" | null
        "ERP_K3CLOUD" | "1234"   | "fieldKey2" | false   | "{\"splicing_formula\":\"\",\"isMainAttribute\": false}"                      | "name1" | "1234"           | null
        "ERP_K3CLOUD" | "1234"   | "fieldKey2" | false   | "{\"splicing_formula\":\"\",\"isMainAttribute\": true}"                       | "name1" | "1234"           | "1234"
        "ERP_K3CLOUD" | "123"    | "fieldKey3" | false   | "{}"                                                                          | "name"  | "123"            | null
        "ERP_K3CLOUD" | "123"    | "fieldKey3" | true    | "{\"splicing_formula\":\"1234+\$number\$+41234\",\"isMainAttribute\": false}" | "name"  | "123"            | null
        "ERP_K3CLOUD" | "123"    | "fieldKey3" | true    | "{\"splicing_formula\":\"1234+\$number\$+41234\",\"isMainAttribute\": true}"  | "name"  | "123"            | null

    }


//    def "CovertFieldValueBigDecimal"() {
//        SpecialFieldPreprocessManager specialFieldPreprocessManager = new SpecialFieldPreprocessManager();
//        ErpConnectInfoEntity connectInfo = new ErpConnectInfoEntity()
//        connectInfo.setChannel(ErpChannelEnum.valueOf(channel))
//        ObjectData objectData = new ObjectData();
//        objectData.put("name", name)
//        ErpObjectFieldEntity fieldEntity = new ErpObjectFieldEntity()
//        fieldEntity.setFieldExtendValue(fieldExtend)
//        when:
//        specialFieldPreprocessManager.covertFieldValueBigDecimal(connectInfo, oldValue, fieldKey, objectData, crm2Erp, fieldEntity)
//        then:
//        objectData.get(fieldKey) == result
//        where:
//        channel       | oldValue           | fieldKey    | crm2Erp | fieldExtend          | name    | result
//        "ERP_K3CLOUD" | "16837.0014000"    | "fieldKey1" | false   | "{\"round_mode\":4}" | "name"  | new BigDecimal("16837.0014")
//        "ERP_K3CLOUD" | "1683775.02341000" | "fieldKey2" | false   | "{\"round_mode\":2}" | "name1" | new BigDecimal("1683775.02")
//        "ERP_K3CLOUD" | "168377.000320"    | "fieldKey3" | false   | "{\"round_mode\":3}" | "name"  | new BigDecimal("168377.000")
//        "ERP_K3CLOUD" | "168377"           | "fieldKey3" | false   | "{\"round_mode\":3}" | "name"  | new BigDecimal("168377.000")
//        "ERP_K3CLOUD" | "16837.00000"      | "fieldKey1" | true    | "{\"round_mode\":4}" | "name"  | new BigDecimal("16837.0000")
//        "ERP_K3CLOUD" | "16837.0014000"    | "fieldKey1" | true    | "{\"round_mode\":4}" | "name"  | new BigDecimal("16837.0014")
//        "ERP_K3CLOUD" | "1683775.02341000" | "fieldKey2" | true    | "{\"round_mode\":2}" | "name1" | new BigDecimal("1683775.02")
//        "ERP_K3CLOUD" | "168377.000320"    | "fieldKey3" | true    | "{\"round_mode\":3}" | "name"  | new BigDecimal("168377.000")
//    }

    @Unroll
    def "CovertFieldValueDateFormat"() {
        SpecialFieldPreprocessManager specialFieldPreprocessManager = new SpecialFieldPreprocessManager();
        ErpConnectInfoEntity connectInfo = new ErpConnectInfoEntity()
        connectInfo.setChannel(ErpChannelEnum.valueOf(channel))
        ObjectData objectData = new ObjectData();
        ErpObjectFieldEntity fieldEntity = new ErpObjectFieldEntity()
        fieldEntity.setFieldExtendValue(fieldExtend)
        when:
        specialFieldPreprocessManager.covertFieldValueDateFormat(connectInfo, oldValue, ErpFieldTypeEnum.valueOf(fieldType), fieldKey, objectData, crm2Erp, fieldEntity)
        then:
        objectData.get(fieldKey) == result
        where:
        fieldType   | channel       | oldValue                | fieldKey    | crm2Erp | fieldExtend                              | result
        "date_time" | "ERP_K3CLOUD" | "1683775526000"         | "fieldKey1" | true    | "{\"format\":\"yyyy年MM月dd日 hh时mm分ss秒\"}" | "2023年05月11日 11时25分26秒"
        "date_time" | "ERP_K3CLOUD" | "1683775526000"         | "fieldKey2" | true    | "{\"format\":\"yyyy/MM/dd hh/mm\"}"      | "2023/05/11 11/25"
        "date_time" | "ERP_K3CLOUD" | "1683775526000"         | "fieldKey3" | true    | "{\"format\":\"yyyy-MM-dd hh:mm:ss\"}"   | "2023-05-11 11:25:26"
        "date_time" | "ERP_K3CLOUD" | "2023年05月11日 11时25分26秒" | "fieldKey1" | false   | "{\"format\":\"yyyy年MM月dd日 hh时mm分ss秒\"}" | 1683775526000L
        "date_time" | "ERP_K3CLOUD" | "2023/05/11 11/25"      | "fieldKey2" | false   | "{\"format\":\"yyyy/MM/dd hh/mm\"}"      | 1683775500000L
        "date_time" | "ERP_K3CLOUD" | "2023-05-11 11:25:26"   | "fieldKey3" | false   | "{\"format\":\"yyyy-MM-dd hh:mm:ss\"}"   | 1683775526000L
        "date"      | "ERP_K3CLOUD" | "1683734400000"         | "fieldKey1" | true    | "{\"format\":\"yyyy年MM月dd日\"}"           | "2023年05月11日"
        "date"      | "ERP_K3CLOUD" | "1683820800000"         | "fieldKey2" | true    | "{\"format\":\"yyyy/MM/dd\"}"            | "2023/05/12"
        "date"      | "ERP_K3CLOUD" | "1683820800000"         | "fieldKey3" | true    | "{\"format\":\"yyyy-MM-dd\"}"            | "2023-05-12"
        "date"      | "ERP_K3CLOUD" | "2023年05月12日"           | "fieldKey1" | false   | "{\"format\":\"yyyy年MM月dd日\"}"           | 1683820800000L
        "date"      | "ERP_K3CLOUD" | "2023/05/12"            | "fieldKey2" | false   | "{\"format\":\"yyyy/MM/dd\"}"            | 1683820800000L
        "date"      | "ERP_K3CLOUD" | "2023-05-11"            | "fieldKey3" | false   | "{\"format\":\"yyyy-MM-dd\"}"            | 1683734400000L
        "date_time" | "ERP_K3CLOUD" | "1683775526000"         | "fieldKey3" | true    | ""                                       | "2023-05-11T11:25:26"
        "date_time" | "ERP_K3CLOUD" | "2023-05-11T11:25:26"   | "fieldKey1" | false   | ""                                       | 1683775526000L
        "date"      | "ERP_K3CLOUD" | "1683734400000"         | "fieldKey3" | true    | ""                                       | "2023-05-11"
        "date"      | "ERP_K3CLOUD" | "2023-05-11"            | "fieldKey1" | false   | ""                                       | 1683734400000L
        "date_time" | "ERP_K3CLOUD" | "20230511112526"        | "fieldKey1" | false   | "{\"format\":\"yyyyMMddhhmmss\"}"        | 1683775526000L
        "date"      | "ERP_K3CLOUD" | "20230511"              | "fieldKey1" | false   | "{\"format\":\"yyyyMMdd\"}"              | 1683734400000L
        "date"      | "ERP_K3CLOUD" | "1683820800000"         | "fieldKey1" | false   | "{\"format\":\"yyyyMMdd\"}"              | null
        "date"      | "ERP_K3CLOUD" | "1.6838208E12"         | "fieldKey2" | true    | "{\"format\":\"yyyy/MM/dd\"}"            | "2023/05/12"
        "date"      | "ERP_K3CLOUD" | "2024/06/11"            | "fieldKey1" | false   | "{\"format\":\"MM月dd日\"}"                                       | null
        "date_time"      | "ERP_K3CLOUD" | "2024/06/11"            | "fieldKey1" | false   | "{\"format\":\"MM月dd日\"}"                                       | null
    }
}
