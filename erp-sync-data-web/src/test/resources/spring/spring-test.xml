<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <import resource="classpath*:spring/web-common.xml"/>
    <import resource="classpath*:spring/web-dubbo-provider.xml"/>
    <import resource="classpath*:spring/web-dubbo-consumer.xml"/>
    <import resource="classpath*:spring/web-mq-producer.xml"/>
    <import resource="classpath*:spring/common-spring.xml"/>
    <import resource="classpath*:spring/syncmain-context.xml"/>
    <import resource="classpath*:spring/syncconverter-context.xml"/>
    <import resource="classpath*:spring/syncwriter-context.xml"/>
    <import resource="classpath*:spring/common-db-proxy.xml"/>
    <import resource="classpath*:spring/admin-context.xml"/>
    <import resource="classpath*:spring/erp-preprocess-data.xml"/>
    <import resource="classpath*:spring/erp-apiproxy-data.xml"/>
    <import resource="classpath:spring/fs-spring-dubbo-rest-plugin-provider.xml"/>
    <import resource="classpath:spring/erp-sync-data-web-rest-client.xml"/>

    <!--  迁移服务  -->
    <import resource="classpath:spring/transfer-sharding-mongo.xml"/>
    <context:component-scan base-package="com.fxiaoke.open.erpsyncdata.web">
        <context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.RestController"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
        <context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.ControllerAdvice"/>
        <context:exclude-filter type ="assignable" expression="com.fxiaoke.open.erpsyncdata.web.config.Swagger2Config"/>
    </context:component-scan>
</beans>