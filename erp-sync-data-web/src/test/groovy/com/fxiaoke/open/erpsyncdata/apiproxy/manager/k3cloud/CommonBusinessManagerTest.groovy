//package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud
//
//import com.fxiaoke.open.erpsyncdata.BaseSpockTest
//import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient
//import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam
//import org.junit.Ignore
//import org.junit.Test
//import org.springframework.beans.factory.annotation.Autowired
//
///**
// *
// * <AUTHOR> (^_−)☆
// * @date 2020/11/24
// */
//@Ignore
//class CommonBusinessManagerTest extends BaseSpockTest {
//    @Autowired
//    private CommonBusinessManager commonBusinessManager;
//
//    @Test
//    public void getIdByNumberTest() {
//        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
//                "http://*************/K3Cloud/",
//                "5ec229fad54306", "ces1", "8888888");
//        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "")
//        def id = commonBusinessManager.getIdByNumber(
//                "BD_MATERIAL", "00001", "FMATERIALID", "FNumber", apiClient)
//        println(id)
//    }
//}
