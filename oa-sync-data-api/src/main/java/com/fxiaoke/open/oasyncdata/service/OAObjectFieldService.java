package com.fxiaoke.open.oasyncdata.service;

import com.fxiaoke.open.oasyncdata.model.OAObjectFieldVO;
import com.fxiaoke.open.oasyncdata.result.base.Result;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2021/3/18
 */
public interface OAObjectFieldService {

    Result<Map<String,List<OAObjectFieldVO>>> getObjectField(String tenantId,String lang);

    Result<List> addOrUpdateObjectField(String tenantId, List<OAObjectFieldVO> oaObjFieldEntityList, String lang);

    Result<List> addOrUpdateObjectFieldByApiName(String tenantId, String apiName,String prefix, String lang);

    Result<List> addOrUpdateSupportField(String tenantId, String apiName,String replaceName,String fieldApiName,String label);
}
