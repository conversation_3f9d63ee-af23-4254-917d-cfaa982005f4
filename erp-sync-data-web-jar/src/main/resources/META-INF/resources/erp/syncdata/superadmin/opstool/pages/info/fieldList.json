{"type": "page", "title": "ERP字段", "remark": null, "name": "fieldList", "toolbar": [], "body": [{"type": "crud", "name": "fields", "api": {"url": "../listFields", "sendOn": "objApiName != null"}, "loadDataOnce": true, "defaultParams": {"perPage": 100}, "headerToolbar": ["export-csv", "reload", "bulkActions", {"type": "tpl", "tpl": "修改按钮也可以新增和删除"}], "bulkActions": [], "filter": {"mode": "horizontal", "body": [{"size": "sm", "label": "tenantId", "type": "input-text", "name": "tenantId", "onEvent": {"blur": {"actions": [{"actionType": "reload", "componentId": "dcIdSelect"}]}}}, {"id": "dcIdSelect", "name": "dcId", "label": "数据中心", "type": "select", "size": "lg", "labelField": "dataCenterName", "valueField": "id", "selectFirst": true, "clearable": true, "source": {"method": "get", "url": "../listDcInfos?tenantId=${tenantId}", "sendOn": "tenantId != null", "autoRefresh": false}, "searchable": true}, {"label": "对象", "type": "input-text", "name": "objApiName"}, {"label": "fieldType", "type": "input-text", "name": "fieldType"}, {"label": "searchStr", "type": "input-text", "name": "searchStr"}]}, "columns": [{"name": "id", "label": "id"}, {"name": "fieldApiName", "label": "fieldApiName"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "required", "label": "required", "type": "status"}, {"name": "fieldDefineType", "label": "fieldDefineType"}, {"name": "fieldExtendValue", "label": "fieldExtendValue"}, {"name": "createTime", "label": "创建时间", "type": "date", "format": "YYYY-MM-DD HH:mm:ss.SSS", "valueFormat": "unix"}, {"name": "updateTime", "label": "修改时间", "type": "date", "format": "YYYY-MM-DD HH:mm:ss.SSS", "valueFormat": "unix"}, {"type": "operation", "label": "操作", "buttons": [{"label": "修改", "type": "button", "actionType": "drawer", "drawer": {"title": "修改", "width": "50%", "body": {"type": "form", "horizontal": {"leftFixed": "md"}, "api": "post:../upsertField", "body": [{"type": "html", "html": "<p>id置空就是新增，tenantId拼接后缀相当于删除数据</p>"}, {"name": "id", "label": "id", "type": "input-text"}, {"name": "tenantId", "label": "tenantId", "type": "input-text"}, {"name": "dataCenterId", "label": "dataCenterId", "type": "static"}, {"name": "channel", "label": "channel", "type": "static"}, {"name": "erpObjectApiName", "label": "erpObjectApiName", "type": "static"}, {"name": "fieldApiName", "label": "fieldApiName", "type": "input-text"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>", "type": "input-text"}, {"name": "required", "label": "required", "type": "switch", "value": "false"}, {"name": "fieldDefineType", "label": "fieldDefineType", "type": "input-text", "value": "text"}, {"name": "fieldExtendValue", "label": "fieldExtendValue", "type": "input-text"}]}}}]}], "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据", "combineNum": 0}]}