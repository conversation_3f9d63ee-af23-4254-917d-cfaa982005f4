package com.fxiaoke.open.erpsyncdata.web.controller.setUp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.arg.CepArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.ObjectApiNameArg;
import com.fxiaoke.open.erpsyncdata.admin.model.*;
import com.fxiaoke.open.erpsyncdata.admin.service.ConnectInfoService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjPresetService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectFieldsService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectService;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CSaveAction;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.cosntant.K3DocumentStatusEnum;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.ObjectDescArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.IdSaveExtend;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.SaveArg;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3CreateConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3UltimateSignatureConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.CascaderInfoAndObjResult;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.*;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpObjTreeNode;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpObjectSimpleInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectRelationshipResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.UpdateErpObjectResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.open.erpsyncdata.web.annontation.ManagedTenantIntercept;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 10:27 2020/8/18
 * @Desc:
 */
@Slf4j
@Api(tags = "erp对象设置相关接口")
@RestController("setUpErpObjectController")
@RequestMapping("cep/setUp/erpObject")
@ManagedTenantIntercept
public class ErpObjectController extends AsyncSupportController {
    @Autowired
    private ErpObjectService erpObjectService;
    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService;
    @Autowired
    private ErpObjPresetService erpObjPresetService;
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private ConnectInfoService connectInfoService;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    @ApiOperation(value = "获取所有虚拟erp对象信息")
    @RequestMapping(value = "/queryErpObject", method = RequestMethod.POST)
    public Result<List<ErpObjectDescResult>> queryErpObject(@RequestBody CepArg cepArg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = getDcId();
        return erpObjectService.queryErpFakeObjectByTenantIdAndDcId(tenantId, userId, dataCenterId);
    }

    @ApiOperation(value = "获取erp虚拟主对象信息")
    @RequestMapping(value = "/queryErpFakeMasterObject", method = RequestMethod.POST)
    public Result<List<ErpObjectDescResult>> queryErpFakeMasterObject(@RequestBody ErpDcIdArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = arg.getDcId();
        if (StringUtils.isBlank(dataCenterId)) {
            dataCenterId = getDcId();
        }
        return erpObjectService.queryErpFakeMasterObjectByTenantId(tenantId, userId, dataCenterId);
    }

    @ApiOperation(value = "返回全部获取主对象信息-erp虚拟主对象/crm主对象")
    @PostMapping(value = "/queryAllErpFakeMasterObject")
    public Result<List<CascaderInfoAndObjResult>> queryAllErpFakeMasterObject(@RequestBody(required = false) CepArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpObjectService.queryAllErpFakeMasterObject(tenantId, userId);
    }

    @ApiOperation(value = "获取erp虚拟从对象信息")
    @RequestMapping(value = "/queryErpFakeDetailObject", method = RequestMethod.POST)
    public Result<List<ErpObjectDescResult>> queryErpFakeDetailObject(@RequestBody ErpObjectApiNameArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = arg.getDcId();
        if (StringUtils.isBlank(dataCenterId)) {
            dataCenterId = getDcId();
        }
        return erpObjectService.queryErpFakeDetailObjectByMasterApiNameAndDcId(tenantId, userId, arg.getErpObjectApiName(), dataCenterId);
    }

    @ApiOperation(value = "获取对象简单信息")
    @RequestMapping(value = "/simple")
    public Result<ErpObjectSimpleInfo> getErpObjSimpleInfo(@RequestBody ObjectApiNameArg arg,
                                                           @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpObjectService.getErpObjSimpleInfo(tenantId, userId, arg.getObjectApiName(),lang);
    }

    @ApiOperation(value = "更新或者新增erp对象信息")
    @RequestMapping(value = "/updateErpObject", method = RequestMethod.POST)
    public Result<UpdateErpObjectResult> updateErpObject(@RequestBody ErpObjectRelationshipResult erpObjectRelationshipResult,
                                                         @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = getDcId();
        return erpObjectService.updateErpObjects(tenantId, dataCenterId, userId, erpObjectRelationshipResult,lang);
    }

    @ApiOperation(value = "根据真实对象获取对象信息")
    @RequestMapping(value = "/queryErpObjectByActualObj", method = RequestMethod.POST)
    public Result<ErpObjectRelationshipResult> queryErpObjectByActualObj(@RequestBody QueryErpObjectByActualObjArg arg,
                                                                         @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        return erpObjectService.getErpObjectRelationshipByRealObjApiName(tenantId, arg.getErpObjectApiName(), arg.getSplitSeq(), dataCenterId,lang);
    }

    @ApiOperation(value = "删除erp对象信息")
    @RequestMapping(value = "/deleteErpObject", method = RequestMethod.POST)
    public Result<String> deleteErpObject(@RequestBody DeleteErpObjectArg deleteArg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpObjectService.deleteErpObject(tenantId, userId, deleteArg);
    }

    @ApiOperation(value = "获取erp真实对象信息列表")
    @RequestMapping(value = "/queryRealErpObjectList", method = RequestMethod.POST)
    public Result<List<ErpObjectRelationshipResult>> queryRealErpObjectList(@RequestBody PageArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = getDcId();
        return erpObjectService.queryRealErpObjectByTenantIdAndDcId(tenantId, userId, arg.getQueryStr(), dataCenterId);
    }

    /**
     * 当resultCode为's306240238',说明解析进行中，前端需要等待
     */
    @ApiOperation(value = "获取K3对象,异步")
    @RequestMapping(value = "analyzeK3Obj", method = RequestMethod.POST)
    public DeferredResult<Result<ErpObjTreeNode>> asyncAnalyzeK3Obj(@RequestBody InitK3Obj.AnalyzeObjArg analyzeObjArg,
                                                                        @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String actionName = i18NStringManager.get(I18NStringEnum.s2029,lang,getLoginUserTenantId());
        DeferredResult<Result<ErpObjTreeNode>> resultDeferredResult = asyncExecuteWithTimeOutResult(actionName,
                (deferredResult) -> {
                    Result<ErpObjTreeNode> analyzeK3Obj = erpObjPresetService.analyzeK3Obj(getLoginUserTenantId(), getDcId(), analyzeObjArg, lang);
                    analyzeK3Obj.setI18nKey(null);
                    analyzeK3Obj.setI18nExtra(null);
                    deferredResult.setResult(analyzeK3Obj);
                },
                10, Result.newError(ResultCodeEnum.ANALYZING_K3_OBJ)
        );
        return resultDeferredResult;
    }

    @ApiOperation(value = "预置对象和字段,带有清理脏数据功能")
    @RequestMapping(value = "preSetK3Obj", method = RequestMethod.POST)
    public DeferredResult<Result<String>> preSetK3Obj(@RequestBody InitK3Obj.PresetObjArg arg,
                                                      @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        final String tenantId = getLoginUserTenantId();
        DeferredResult<Result<String>> resultDeferredResult = asyncExecute(
                () -> erpObjPresetService.preSetK3Obj(tenantId, getDcId(), arg,lang),
                10,
                false,
                i18NStringManager.get2(I18NStringEnum.s108.getI18nKey(),lang, tenantId,i18NStringManager.get2(I18NStringEnum.s3755, lang, tenantId) + arg.getErpObjectApiName(), Lists.newArrayList(arg.getErpObjectApiName())),
                lang
        );
        return resultDeferredResult;
    }


    @ApiOperation(value = "获取对象列表，只有主对象")
    @RequestMapping(value = "parseObjList", method = RequestMethod.POST)
    public DeferredResult<Result<List<ErpObjTreeNode>>> parseObjList(@RequestBody CepArg arg) {
        DeferredResult<Result<List<ErpObjTreeNode>>> resultDeferredResult = asyncExecuteWithTimeOutResult(
                i18NStringManager.get(I18NStringEnum.s1085, getLang(), getLoginUserTenantId()),
                (deferredResult) -> deferredResult.setResult(erpObjPresetService.parseObjectList(getLoginUserTenantId(), getDcId())),
                10, Result.newSystemError(I18NStringEnum.s3708)
        );
        return resultDeferredResult;
    }

    /**
     * 当resultCode为's306240273',说明解析进行中，前端需要等待
     */
    @ApiOperation(value = "获取对象,异步")
    @RequestMapping(value = "parseObj", method = RequestMethod.POST)
    public DeferredResult<Result<ErpObjTreeNode>> parseObj(@RequestBody ObjectDescArg.ParseObjTree arg) {
        DeferredResult<Result<ErpObjTreeNode>> resultDeferredResult = asyncExecuteWithTimeOutResult(
                i18NStringManager.get(I18NStringEnum.s3301, getLang(), getLoginUserTenantId()),
                (deferredResult) -> deferredResult.setResult(erpObjPresetService.parseObject(getLoginUserTenantId(), getDcId(), arg)),
                10, Result.newError(ResultCodeEnum.PARSING_OBJ)
        );
        return resultDeferredResult;
    }

    @ApiOperation(value = "预置对象和字段")
    @RequestMapping(value = "presetObj", method = RequestMethod.POST)
    public DeferredResult<Result<String>> presetObj(@RequestBody ObjectDescArg.ParseObjField arg,
                                                    @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        DeferredResult<Result<String>> resultDeferredResult = asyncExecute(
                () -> erpObjPresetService.presetObject(getLoginUserTenantId(), getDcId(), arg),
                10,
                false,
                i18NStringManager.get(I18NStringEnum.s3769, lang, getLoginUserTenantId()) + arg.getRealObjectApiName(),
                lang
        );
        return resultDeferredResult;
    }

    @ApiOperation(value = "获取K3批量查询条件")
    @PostMapping("getK3FilterData")
    public Result<GetK3FilterData.Result> getK3FilterData(@RequestBody GetK3FilterData.Arg arg) {
        final List<List<FilterData>> filterData = erpDataPreprocessService.getFilterData(getLoginUserTenantId(), getDcId(), ErpChannelEnum.ERP_K3CLOUD.name(), arg.getObjectApiName(), Objects.equals(arg.getType(), 2) ? ErpObjInterfaceUrlEnum.queryInvalid : ErpObjInterfaceUrlEnum.queryMasterBatch);
        return Result.newSuccess(new GetK3FilterData.Result(filterData));
    }

    /**
     * 产品不用这个需求了
     */
    @Deprecated
    @ApiOperation(value = "生成FilterString")
    @PostMapping("generateK3FilterString")
    public Result<GenerateK3FilterString.Result> generateK3FilterString(@RequestBody GenerateK3FilterString.Arg arg,
                                                                        @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        // 空List allMatch 结果一定为true
        final List<List<FilterData>> filters = FilterDataVo.convert2Dto(arg.getFilters());
        checkFilters(filters,lang,getLoginUserTenantId());
        final TimeFilterArg timeFilterArg = JSON.parseObject(JSON.toJSONString(arg), TimeFilterArg.class);
        timeFilterArg.setTenantId(getLoginUserTenantId());
        final String filterStr = k3DataManager.getFilterString(timeFilterArg);
        return Result.newSuccess(new GenerateK3FilterString.Result(filterStr));
    }

    private void checkFilters(final List<List<FilterData>> filters,String lang,String tenantId) {
        if (!filters.stream().allMatch(list -> list.stream().anyMatch(FilterData::getIsVariableBetween))) {
            throw new ErpSyncDataException(ResultCodeEnum.PARAM_ILLEGAL,
                    i18NStringManager.get2(I18NStringEnum.s109, lang, tenantId, JSON.toJSONString(filters)),
                    null,
                    null);
        }
    }

    @ApiOperation(value = "保存K3批量查询条件")
    @PostMapping("setK3FilterData")
    public Result<Void> setK3FilterData(@RequestBody SetK3FilterData.Arg arg,
                                        @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        // 空List allMatch 结果一定为true
        checkFilters(FilterDataVo.convert2Dto(arg.getFilters()),lang,getLoginUserTenantId());

        final ErpObjInterfaceUrlEnum erpObjInterfaceUrlEnum = Objects.equals(arg.getType(), 2) ? ErpObjInterfaceUrlEnum.queryInvalid : ErpObjInterfaceUrlEnum.queryMasterBatch;
        erpDataPreprocessService.setFilterData(getLoginUserTenantId(), getDcId(), ErpChannelEnum.ERP_K3CLOUD.name(), arg.getObjectApiName(), FilterDataVo.convert2Dto(arg.getFilters()), erpObjInterfaceUrlEnum);
        return Result.newSuccess();
    }

    @ApiOperation(value = "设置K3对象新增状态")
    @PostMapping("setK3ObjectAddStatus")
    public Result<Void> setK3ObjectAddStatus(@RequestBody SetK3ObjectSaveExtend.Arg arg,
                                             @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        final Result<Void> voidResult = erpObjectFieldsService.setK3ObjectAddStatus(getLoginUserTenantId(), getDcId(), arg.getObjectApiName(), K3CSaveType.getByStatus(arg.getSaveType()), lang);
        if (!voidResult.isSuccess()) {
            return voidResult;
        }

        saveK3SaveConfig(getLoginUserTenantId(), getDcId(), arg.getObjectApiName(), arg.getConfig());

        return Result.newSuccess();
    }

    @ApiOperation(value = "设置K3对象修改后状态")
    @PostMapping("setK3ObjectModifyStatus")
    public Result<Void> setK3ObjectModifyStatus(@RequestBody SetK3ObjectSaveExtend.Arg arg,
                                                @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        return erpObjectFieldsService.setK3ObjectModifyStatus(getLoginUserTenantId(), getDcId(), arg.getObjectApiName(), K3CSaveType.getByStatus(arg.getSaveType()), lang);
    }

    @ApiOperation(value = "获取K3对象保存类型")
    @PostMapping("getK3ObjectSaveExtend")
    public Result<GetK3ObjectSaveExtend.Result> getK3ObjectSaveExtend(@RequestBody GetK3ObjectSaveExtend.Arg arg) {
        final String dcId = getDcId();
        final String tenantId = getLoginUserTenantId();

        final IdSaveExtend saveExtend = erpObjectFieldsService.getK3ObjectSaveExtend(tenantId, dcId, arg.getObjectApiName());

        if (Objects.isNull(saveExtend)) {
            return Result.newSuccess(new GetK3ObjectSaveExtend.Result());
        }

        final String modifyStatus = StringUtils.isBlank(saveExtend.getModifyStatus()) ? K3CSaveType.MODIFY.getStatus() : saveExtend.getModifyStatus();

        return Result.newSuccess(new GetK3ObjectSaveExtend.Result(K3CSaveType.getSaveType(saveExtend).getStatus(), modifyStatus, getK3SaveConfig(tenantId, dcId, arg.getObjectApiName())));
    }

    @ApiOperation(value = "获取所有K3保存状态")
    @PostMapping("getK3AllSaveStatus")
    public Result<GetK3AllSaveStatus.Result> getK3AllSaveStatus(@RequestBody GetK3AllSaveStatus.Arg arg) {
        final String dcId = getDcId();
        final String tenantId = getLoginUserTenantId();

        // 获取前缀
        ConnectInfoResult connectInfo = connectInfoService.getConnectInfoByDataCenterId(tenantId, -10000, dcId).safeData();

        final ConnectInfoResult.ConnectParams connectParams = connectInfo.getConnectParams();
        if (Objects.isNull(connectParams) || Objects.isNull(connectParams.getK3Cloud())) {
            return Result.newError(ResultCodeEnum.CONNECT_INFO_NOT_EXISTS.getErrCode(), I18NStringEnum.s388);
        }

        // 获取4个动作
        String k3baseUrl = connectParams.getK3Cloud().getBaseUrl();
        String baseUrl = !k3baseUrl.endsWith("/") ? k3baseUrl + "/" : k3baseUrl;

        final List<GetK3AllSaveStatus.ActionConf> defaultAddActions = Arrays.stream(K3CSaveType.values())
                .map(K3CSaveType::getAddActions)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .distinct()
                .map(K3CSaveAction::getK3CSaveActionByType)
                .map(action -> new GetK3AllSaveStatus.ActionConf(action.getType(), baseUrl + action.getUrlSuffix()))
                .collect(Collectors.toList());
        final String addJsonString = JSON.toJSONString(defaultAddActions);

        final List<GetK3AllSaveStatus.SaveStatus> addList = Arrays.stream(K3CSaveType.values())
                .filter(type -> CollectionUtils.isNotEmpty(type.getAddActions()))
                .map(type -> {
                    final List<GetK3AllSaveStatus.ActionConf> actions = JSON.parseArray(addJsonString, GetK3AllSaveStatus.ActionConf.class);
                    actions.forEach(actionConf -> actionConf.setSelected(type.getAddActions().contains(actionConf.getAction())));
                    return new GetK3AllSaveStatus.SaveStatus(type.getStatus(), actions);
                })
                .collect(Collectors.toList());

        final List<GetK3AllSaveStatus.ActionConf> defaultModifyActions = Arrays.stream(K3CSaveType.values())
                .map(K3CSaveType::getModifyActions)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .distinct()
                .map(K3CSaveAction::getK3CSaveActionByType)
                .sorted(Comparator.comparing(K3CSaveAction::getOrder))
                .map(action -> new GetK3AllSaveStatus.ActionConf(action.getType(), baseUrl + action.getUrlSuffix()))
                .collect(Collectors.toList());
        final String modifyJsonString = JSON.toJSONString(defaultModifyActions);
        final List<GetK3AllSaveStatus.SaveStatus> modifyList = Arrays.stream(K3CSaveType.values())
                .filter(type -> CollectionUtils.isNotEmpty(type.getModifyActions()))
                .map(type -> {
                    final List<GetK3AllSaveStatus.ActionConf> actions = JSON.parseArray(modifyJsonString, GetK3AllSaveStatus.ActionConf.class);
                    actions.forEach(actionConf -> actionConf.setSelected(type.getModifyActions().contains(actionConf.getAction())));
                    return new GetK3AllSaveStatus.SaveStatus(type.getStatus(), actions);
                })
                .collect(Collectors.toList());

        return Result.newSuccess(new GetK3AllSaveStatus.Result(addList, modifyList, GetK3AllSaveStatus.KeyType.convert(ConfigCenter.k3PresetSaveConfigKey)));
    }

    @ApiOperation(value = "设置K3对象创建配置 包括提交/审核失败后的处理方式")
    @PostMapping("setK3CreateConfig")
    public Result<Void> setK3CreateConfig(@RequestBody SetK3CreateConfig.Arg arg) {
        if (checkK3CreateConfigIllegal(arg.getConfig())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR.getErrCode(), I18NStringEnum.s389);
        }

        final String dcId = getDcId();
        final String tenantId = getLoginUserTenantId();

        final String realObjApiName = erpObjManager.getRealObjApiName(tenantId, arg.getObjectApiName());

        final Map<String, K3CreateConfig> k3CreateConfigMap = configCenterConfig.getK3CreateConfig(tenantId, dcId);
        k3CreateConfigMap.put(realObjApiName, arg.getConfig());
        tenantConfigurationManager.updateConfig(tenantId, dcId, ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.K3C_CREATE_CONFIG.name(), JacksonUtil.toJson(k3CreateConfigMap));
        return Result.newSuccess();
    }

    private boolean checkK3CreateConfigIllegal(K3CreateConfig config) {
        if (config.getReturnStepStatus().equals(K3DocumentStatusEnum.ADD.getStatus())) {
            return config.isDeleteByAuditFail() || config.isDeleteBySubmitFail();
        } else if (config.getReturnStepStatus().equals(K3DocumentStatusEnum.AUDITING.getStatus())) {
            return config.isDeleteByAuditFail();
        }
        return false;
    }

    @ApiOperation(value = "获取K3对象创建配置 包括提交/审核失败后的处理方式")
    @PostMapping("getK3CreateConfig")
    public Result<GetSubmitAuditFailHandleType.Result> getK3CreateConfig(@RequestBody GetSubmitAuditFailHandleType.Arg arg) {
        final String dcId = getDcId();
        final String tenantId = getLoginUserTenantId();
        final String realObjApiName = erpObjManager.getRealObjApiName(tenantId, arg.getObjectApiName());

        final K3CreateConfig k3CreateConfig = configCenterConfig.getK3CreateConfig(tenantId, dcId, realObjApiName);
        return Result.newSuccess(new GetSubmitAuditFailHandleType.Result(k3CreateConfig));
    }

    private Map<String,Map<String, Object>> getK3SaveConfig(String tenantId, String dcId, String realObjectApiName) {
        final Map<String,Map<String, Object>> result = new HashMap<>();
        //保存接口参数
        ErpTenantConfigurationEntity erpTenantConfigurationEntity = tenantConfigurationManager.findNoCache(tenantId, dcId, ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.ERP_K3_OBJ_SAVE_ARG_SETTING);
        if (ObjectUtils.isNotEmpty(erpTenantConfigurationEntity)) {//按企业对象的
            String configuration = erpTenantConfigurationEntity.getConfiguration();
            Map<String, Map<String, Object>> config = JSONObject.parseObject(configuration, Map.class);
            if (MapUtils.isNotEmpty(config) && MapUtils.isNotEmpty(config.get(realObjectApiName))) {
                result.put(K3CSaveAction.SAVE.getType().toString(),config.get(realObjectApiName));
            }
        }
        if(!result.containsKey(K3CSaveAction.SAVE.getType())){
            result.put(K3CSaveAction.SAVE.getType().toString(), Maps.newHashMap());
        }
        //其他接口参数
        erpTenantConfigurationEntity = tenantConfigurationManager.findNoCache(tenantId, dcId, ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.ERP_K3_OBJ_INTERFACE_ARG_SETTING);
        if (ObjectUtils.isNotEmpty(erpTenantConfigurationEntity)) {//按企业对象的
            String configuration = erpTenantConfigurationEntity.getConfiguration();
            Map<String, Map<String,Map<String, Object>>> config = JSONObject.parseObject(configuration, Map.class);
            if(config.containsKey(realObjectApiName)){
                result.putAll(config.get(realObjectApiName));
            }
        }

        // subSystemId, isEntryBatchFill ,isVerifyBaseDataField
        final IdSaveExtend k3ObjectSaveExtend = erpObjectFieldsService.getK3ObjectSaveExtend(tenantId, dcId, realObjectApiName);
        if (Objects.isNull(k3ObjectSaveExtend)) {
            return result;
        }

        if (Objects.nonNull(k3ObjectSaveExtend.getSubSystemId())) {
            result.get(K3CSaveAction.SAVE.getType().toString()).put("SubSystemId", k3ObjectSaveExtend.getSubSystemId());
        }
        if (Objects.nonNull(k3ObjectSaveExtend.getIsEntryBatchFill())) {
            result.get(K3CSaveAction.SAVE.getType().toString()).put("IsEntryBatchFill", k3ObjectSaveExtend.getIsEntryBatchFill());
        }
        if (Objects.nonNull(k3ObjectSaveExtend.getIsVerifyBaseDataField())) {
            result.get(K3CSaveAction.SAVE.getType().toString()).put("IsVerifyBaseDataField", k3ObjectSaveExtend.getIsVerifyBaseDataField());
        }

        /** @see SaveArg#SaveArg() IsAutoSubmitAndAudit,IsEntryBatchFill,IsVerifyBaseDataField 一定有 */
        ImmutableMap.of("IsAutoSubmitAndAudit", false, "IsEntryBatchFill", true, "IsVerifyBaseDataField", false).entrySet().stream()
                .filter(entry -> !result.get(K3CSaveAction.SAVE.getType().toString()).containsKey(entry.getKey()))
                .forEach(entry -> result.get(K3CSaveAction.SAVE.getType().toString()).put(entry.getKey(), entry.getValue()));

        return result;
    }

    private void saveK3SaveConfig(String tenantId, String dcId, String realObjectApiName, Map<String,Map<String, Object>> value) {
        if (Objects.isNull(value)) {
            return;
        }
        if(value.containsKey(K3CSaveAction.SAVE.getType().toString())){
            ErpTenantConfigurationEntity erpTenantConfigurationEntity = tenantConfigurationManager.findNoCache(tenantId, dcId, ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.ERP_K3_OBJ_SAVE_ARG_SETTING);
            String configuration = ObjectUtils.isEmpty(erpTenantConfigurationEntity) || StringUtils.isBlank(erpTenantConfigurationEntity.getConfiguration()) ? "{}" : erpTenantConfigurationEntity.getConfiguration();
            Map<String, Map<String, Object>> config = JSONObject.parseObject(configuration, Map.class);
            config.put(realObjectApiName, value.get(K3CSaveAction.SAVE.getType().toString()));
            tenantConfigurationManager.updateConfig(tenantId, dcId, ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.ERP_K3_OBJ_SAVE_ARG_SETTING.name(), JacksonUtil.toJson(config));
            value.remove(K3CSaveAction.SAVE.getType().toString());
        }
        if(CollectionUtils.isNotEmpty(value.keySet())){
            ErpTenantConfigurationEntity erpTenantConfigurationEntity = tenantConfigurationManager.findNoCache(tenantId, dcId, ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.ERP_K3_OBJ_INTERFACE_ARG_SETTING);
            String configuration = ObjectUtils.isEmpty(erpTenantConfigurationEntity) || StringUtils.isBlank(erpTenantConfigurationEntity.getConfiguration()) ? "{}" : erpTenantConfigurationEntity.getConfiguration();
            Map<String, Map<String,Map<String, Object>>> config = JSONObject.parseObject(configuration, Map.class);
            config.put(realObjectApiName, value);
            tenantConfigurationManager.updateConfig(tenantId, dcId, ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.ERP_K3_OBJ_INTERFACE_ARG_SETTING.name(), JacksonUtil.toJson(config));
        }

        }
    @ApiOperation(value = "设置云星空旗舰版签名、解密配置")
    @PostMapping("setK3UltimateSignatureConfig")
    public Result<Void> setK3UltimateSignatureConfig(@RequestBody K3UltimateSignatureConfig arg,
                                             @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId(), dcId=getDcId();
        tenantConfigurationManager.updateConfig(tenantId, dcId, ErpChannelEnum.ERP_K3CLOUD_ULTIMATE.name(), TenantConfigurationTypeEnum.ERP_K3_ULTIMATE_SIGNATURE_CONFIG.name(), JacksonUtil.toJson(arg));
        return Result.newSuccess();
    }
    @ApiOperation(value = "获取云星空旗舰版签名、解密配置")
    @PostMapping("getK3UltimateSignatureConfig")
    public Result<K3UltimateSignatureConfig> getK3UltimateSignatureConfig(@RequestBody K3UltimateSignatureConfig.queryArg arg,
                                                     @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId(), dcId=getDcId();
        K3UltimateSignatureConfig config = tenantConfigurationManager.getK3UltimateSignatureConfig(tenantId, dcId,false);
        return Result.newSuccess(config);
    }
}
