package com.fxiaoke.open.erpsyncdata.web.controller.setUp;

import com.fxiaoke.open.erpsyncdata.admin.arg.CepArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.CepListArg;
import com.fxiaoke.open.erpsyncdata.admin.service.EmployeeMappingService;
import com.fxiaoke.open.erpsyncdata.admin.service.SystemFieldService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BaseArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.PageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryEmployeeMappingListArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SystemFieldMappingArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.EmployeeMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.SpecialFieldMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 9:40 2020/9/3
 * @Desc:
 */
@Slf4j
@Api(tags = "员工绑定相关接口")
@RestController("setUpEmployeeMappingController")
@RequestMapping("cep/setUp/employeeMapping")
public class EmployeeMappingController extends AsyncSupportController {
    @Autowired
    private EmployeeMappingService employeeMappingService;
    @Autowired
    private SystemFieldService systemFieldService;
    @Autowired
    private I18NStringManager i18NStringManager;

    @ApiOperation(value = "获取员工绑定")
    @RequestMapping(value = "/queryEmployeeMapping", method = RequestMethod.POST)
    public Result<QueryResult<List<EmployeeMappingResult>>> queryEmployeeMapping(@RequestBody QueryEmployeeMappingListArg arg,
                                                                                 @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        return employeeMappingService.queryEmployeeMapping(tenantId,userId,dataCenterId,arg,lang);
    }

    @ApiOperation(value = "查询员工或用户绑定数据")
    @RequestMapping(value = "/queryEmpOrUserMapping", method = RequestMethod.POST)
    public Result<QueryResult<List<SpecialFieldMappingResult.EmpResult>>> queryEmpOrUserMapping(@RequestBody QueryEmployeeMappingListArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        return employeeMappingService.queryEmpOrUserMapping(tenantId,userId,dataCenterId,arg);
    }

    @ApiOperation(value = "更新或者新增员工绑定")
    @RequestMapping(value = "/updateEmployeeMapping", method = RequestMethod.POST)
    public Result<String> updateEmployeeMapping(@RequestBody EmployeeMappingResult employeeMappingResult) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        if(employeeMappingResult.getFsEmployeeId()==null||employeeMappingResult.getErpEmployeeId()==null){
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        employeeMappingResult.setDataCenterId(dataCenterId);
        return employeeMappingService.bindEmployeeMappingByFsId(tenantId,userId,employeeMappingResult);
    }

    @ApiOperation(value = "新增或更新员工或用户绑定关系")
    @RequestMapping(value = "/updateEmpOrUserMapping", method = RequestMethod.POST)
    public Result<String> updateEmpOrUserMapping(@RequestBody ErpFieldDataMappingEntity entity,
                                                 @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = getDcId();
        if (StringUtils.isEmpty(entity.getFsDataId())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        if(entity.getDataType() == ErpFieldTypeEnum.employee) {
            if(StringUtils.isEmpty(entity.getErpDataId())) {
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
            }
        }
        entity.setDataCenterId(dataCenterId);
        return employeeMappingService.updateEmpOrUserMapping(tenantId,userId,entity,lang);
    }

    @Deprecated
    @ApiOperation(value = "删除员工绑定")
    @RequestMapping(value = "/deleteEmployeeMapping", method = RequestMethod.POST)
    public Result<QueryResult<List<EmployeeMappingResult>>> deleteEmployeeMapping(@RequestBody BaseArg arg,
                                                                                  @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        return employeeMappingService.batchDeleteEmployeeMapping(tenantId,userId,dataCenterId, Lists.newArrayList(arg.getId()),lang);
    }

    @ApiOperation(value = "批量删除员工或用户映射关系并返回删除后的数据")
    @RequestMapping(value = "/batchDeleteEmpOrUserMapping", method = RequestMethod.POST)
    public Result<Integer> batchDeleteEmpOrUserMapping(@RequestBody SystemFieldMappingArg deleteArg,
                                                       @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        return employeeMappingService.batchDeleteEmpOrUserMapping(tenantId,
                userId,
                dataCenterId,
                deleteArg.getFieldType(),
                deleteArg.getIdList(),
                lang);
    }

    @ApiOperation(value = "获取Erp所有未同步员工")
    @RequestMapping(value = "/queryAllUnSyncEmployee", method = RequestMethod.POST)
    public DeferredResult<Result<QueryResult<List<EmployeeMappingResult>>>> queryAllUnSyncEmployee(@RequestBody PageArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        return asyncExecute(() -> employeeMappingService.queryAllUnSyncEmployee(tenantId, userId, dataCenterId, arg), 12, false, I18NStringEnum.s4503, getLang());
    }

    @ApiOperation(value = "同步erp员工")
    @RequestMapping(value = "/syncEprEmployee", method = RequestMethod.POST)
    public Result<String> syncEprEmployee(@RequestBody CepListArg<EmployeeMappingResult> arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        return employeeMappingService.syncErpEmployee(tenantId,userId,dataCenterId,arg.getCepListArg());
    }

    @ApiOperation(value = "同步erp员工")
    @RequestMapping(value = "/syncAllUnSyncEmployee", method = RequestMethod.POST)
    public DeferredResult<Result<String>> syncAllUnSyncEmployee(@RequestBody CepArg cepArg,
                                                                @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        DeferredResult<Result<String>> result = new DeferredResult<Result<String>>(1000 * 5L, Result.newSuccess(i18NStringManager.get(I18NStringEnum.s102,lang,tenantId)));
        ParallelUtils.createBackgroundTask().submit(() -> {
            Result<String> result1 = employeeMappingService.syncAllUnSyncEmployee(tenantId, userId, dataCenterId);
            result.setResult(result1);
        }).run();
        return result;
    }

    @ApiOperation(value = "导出员工或者用户数据")
    @RequestMapping(value = "/exportEmpOrUserMappingData", method = RequestMethod.POST)
    public DeferredResult<Result<Void>> exportEmpOrUserMappingData(@RequestBody SystemFieldMappingArg arg,
                                                                   @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        DeferredResult<Result<Void>> result = new DeferredResult<>(1000 * 5L, Result.newSuccess(i18NStringManager.get(I18NStringEnum.s103,lang,tenantId)));
        ParallelUtils.createBackgroundTask().submit(() -> {
            Result<Void> result1 = systemFieldService.exportSystemFieldMapping(tenantId,
                    userId,
                    dataCenterId,
                    arg.getFieldType(),
                    arg.getIdList(),
                    lang);
            result.setResult(result1);
        }).run();
        return result;
    }
}
