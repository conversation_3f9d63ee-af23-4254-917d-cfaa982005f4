package com.fxiaoke.open.erpsyncdata.web.controller.setUp;

import cn.hutool.core.util.StrUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.open.erpsyncdata.admin.arg.IdArg;
import com.fxiaoke.open.erpsyncdata.admin.model.ProductModuleCode;
import com.fxiaoke.open.erpsyncdata.admin.model.RedoSyncRecord;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.remote.EnterpriseRelationManager;
import com.fxiaoke.open.erpsyncdata.admin.remote.UserRoleManager;
import com.fxiaoke.open.erpsyncdata.admin.service.ConnectInfoService;
import com.fxiaoke.open.erpsyncdata.admin.service.RelationErpShardService;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RelationErpShardStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationErpShardDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationManageGroupDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationErpShardEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationManageGroupEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamStatProgress;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ManageGroupAddDownstreamFailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ManageGroupAddTaskDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.CheckResultEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ManageGroupAddDownstreamFailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ManageGroupAddTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.DownstreamRelationManage;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ManageGroupAddFailRecode;
import com.fxiaoke.open.erpsyncdata.preprocess.model.UpGroupStreamStat;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.fxiaoke.open.erpsyncdata.web.annontation.CheckProduct;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/3/4 15:08:36
 */

@Slf4j
@Api(tags = "集成代管设置相关接口")
@RestController
@RequestMapping("cep/setUp/relationManage")
@CheckProduct(ProductModuleCode.INTEGRATION_GROUP_APP)
public class IntegrationRelationManageController extends AsyncSupportController {
    @Autowired
    private RelationErpShardDao relationErpShardDao;
    @Autowired
    private RelationErpShardService relationErpShardService;
    @Autowired
    private RelationManageGroupDao relationManageGroupDao;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private EnterpriseRelationManager enterpriseRelationManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private ConnectInfoService connectInfoService;
    @Autowired
    private ManageGroupAddTaskDao manageGroupAddTaskDao;
    @Autowired
    private ManageGroupAddDownstreamFailDao manageGroupAddDownstreamFailDao;
    @Autowired
    private EIEAConverter eieaConverter;

    @ApiOperation(value = "查看所有代管企业组")
    @PostMapping(value = "/queryRelationManage")
    public Result<List<QueryRelationManageResult>> queryRelationManage(@RequestBody IdArg idArg) {
        String tenantId = getLoginUserTenantId();
        String id = idArg.getId();
        final List<RelationManageGroupEntity> entities;
        if (StrUtil.isNotEmpty(id)) {
            RelationManageGroupEntity entity = relationManageGroupDao.getById(tenantId, id);
            entities = Lists.newArrayList(entity);
        } else {
            entities = relationManageGroupDao.queryAllByTenantId(tenantId);
        }

        // 获取企业名称
        final Map<String, String> templateName = entities.stream().map(RelationManageGroupEntity::getTemplateId).distinct().map(templateId -> Pair.of(templateId, userCenterService.getEnterpriseName(templateId))).filter(pair -> Objects.nonNull(pair.getValue())).collect(Collectors.toMap(Pair::getKey, Pair::getValue));

        final List<QueryRelationManageResult> results = entities.stream().map(entity -> {
            final String templateId = entity.getTemplateId();
            final ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(templateId, entity.getDcId());

            final List<String> allDownstreamIdsByGroupId = relationErpShardDao.getAllDownstreamIdsByGroupId(entity.getId(), RelationErpShardStatusEnum.normal.getStatus());
            UpGroupStreamStat upGroupStreamStat = relationErpShardService.calStatGroupByDcLatest(tenantId, templateId, entity.getDcId(), entity.getId()).safeData();
            QueryRelationManageResult.QueryRelationManageResultBuilder builder = QueryRelationManageResult.builder()
                    .id(entity.getId())
                    .name(entity.getName())
                    .templateId(templateId)
                    .templateName(templateName.get(templateId))
                    .dataCenterId(entity.getDcId())
                    .dataCenterName(Objects.isNull(connectInfo) ? null : connectInfo.getDataCenterName())
                    .downstreamCount(allDownstreamIdsByGroupId.size());
            if (upGroupStreamStat != null) {
                builder.lastStatTime(upGroupStreamStat.getLastStatTime())
                        .streamCount(upGroupStreamStat.getStreamCount())
                        .enableStreamCount(upGroupStreamStat.getEnableStreamCount())
                        .warningCount(upGroupStreamStat.getAlertingDownstreamCount())
                        .errorCount(upGroupStreamStat.getFailedDownstreamCount());
            }
            return builder.build();
        }).collect(Collectors.toList());
        return Result.newSuccess(results);
    }


    @ApiOperation(value = "查看模板企业连接器")
    @PostMapping(value = "/getTemplateDataCenter")
    public Result<GetTemplateDataCenter.Result> getTemplateDataCenter(@RequestBody GetTemplateDataCenter.Arg arg) {
        final String tenantId = getLoginUserTenantId();

        final String templateId = userCenterService.getTenantId(arg.getTemplateAccount());
        final Result2<Boolean> templateEnterprise = enterpriseRelationManager.isTemplateEnterprise(tenantId, templateId);
        if (!templateEnterprise.isSuccess()) {
            return Result.copy(Result.copy(templateEnterprise));
        }
        if (BooleanUtils.isNotTrue(templateEnterprise.getData())) {
            return Result.newError(ResultCodeEnum.NOT_TEMPLATE_IN_PARAMETERS);
        }

        final Result<List<DataCenterInfoResult>> allDCInfo = connectInfoService.getAllDCInfo(templateId, -10000, getLang());

        if (!allDCInfo.isSuccess()) {
            return Result.copy(allDCInfo);
        }

        final List<RelationManageGroupEntity> entities = relationManageGroupDao.queryAllByTemplateId(templateId);
        final Set<String> existDcIds = entities.stream().map(RelationManageGroupEntity::getDcId).collect(Collectors.toSet());

        final List<GetTemplateDataCenter.DataCenterInfoWithEnablePloyDetailCount> collect = allDCInfo.getData().stream()
                .filter(info -> !Objects.equals(info.getChannel(), ErpChannelEnum.CRM))
                // 不显示已共享的连接器
                .filter(info -> !existDcIds.contains(info.getId()))
                .map(info -> {
                    final GetTemplateDataCenter.DataCenterInfoWithEnablePloyDetailCount copy = BeanUtil.copy(info, GetTemplateDataCenter.DataCenterInfoWithEnablePloyDetailCount.class);
                    final int count = adminSyncPloyDetailDao.countBySourceOrDestDcIdAndObjApiName(templateId, info.getId(), null, SyncPloyDetailStatusEnum.ENABLE.getStatus(), null);

                    copy.setEnableCount(count);
                    return copy;
                }).collect(Collectors.toList());

        return Result.newSuccess(new GetTemplateDataCenter.Result(collect));
    }

    @ApiOperation(value = "创建代管企业组")
    @PostMapping(value = "/createRelationManageGroup")
    public DeferredResult<Result<AddDownstreamEnterprise.Result>> createRelationManageGroup(@RequestBody CreateRelationManageGroup.Arg arg) {
        final String tenantId = getLoginUserTenantId();
        final String lang = getLang();

        String actionName = i18NStringManager.get(I18NStringEnum.s3603, lang, getLoginUserTenantId());
        return asyncExecute(() -> createRelationManageGroup(tenantId, arg), 10, false, actionName, lang);
    }

    private Result<AddDownstreamEnterprise.Result> createRelationManageGroup(String tenantId, CreateRelationManageGroup.Arg arg) {
        if (StringUtils.isBlank(arg.getName()) || StringUtils.isBlank(arg.getTemplateId()) || StringUtils.isBlank(arg.getDcId())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        final String templateId = String.valueOf(eieaConverter.enterpriseAccountToId(arg.getTemplateId()));

        // 检查模板企业
        final Result2<Boolean> templateEnterprise = enterpriseRelationManager.isTemplateEnterprise(tenantId, templateId);
        if (!templateEnterprise.isSuccess()) {
            return Result.copy(Result.copy(templateEnterprise));
        }
        if (BooleanUtils.isNotTrue(templateEnterprise.getData())) {
            return Result.newError(ResultCodeEnum.NOT_TEMPLATE_IN_PARAMETERS);
        }

        // 检查DC
        final Result<ConnectInfoResult> connectInfoByDataCenterId = connectInfoService.getConnectInfoByDataCenterId(templateId, getLoginUserId(), arg.getDcId());
        if (!connectInfoByDataCenterId.isSuccess()) {
            return Result.copy(connectInfoByDataCenterId);
        }
        if (Objects.isNull(connectInfoByDataCenterId.getData())) {
            return Result.newError(ResultCodeEnum.NOT_FOUND_CONNECTOR);
        }

        // 检查是否已有dcId一样的group
        final RelationManageGroupEntity checkGroup = relationManageGroupDao.queryByTemplateIdAndDcId(templateId, arg.getDcId());
        if (Objects.nonNull(checkGroup)) {
            return Result.newError(ResultCodeEnum.DUPLICATE_RELATION_MANAGE_GROUP);
        }

        // 检查下游企业
        final List<String> downstreamAccounts = arg.getDownstreamAccounts().stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        final List<String> checkIds = userCenterService.getTenantIds(downstreamAccounts);
        final Result<Void> checkResult = relationErpShardService.checkAddDownstreamIds(tenantId, checkIds, templateId);
        if (!checkResult.isSuccess()) {
            return Result.copy(checkResult);
        }

        // 创建group
        final RelationManageGroupEntity group = new RelationManageGroupEntity();
        final String groupId = IdGenerator.get();
        group.setId(groupId);
        group.setTenantId(tenantId);
        group.setName(arg.getName());
        group.setTemplateId(templateId);
        group.setDcId(arg.getDcId());
        group.setCreateTime(System.currentTimeMillis());
        group.setUpdateTime(System.currentTimeMillis());
        final int insert = relationManageGroupDao.insert(group);
        if (insert == 0) {
            log.error("relationManageGroupDao.insert error group:{}", group);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }

        // 校验 对象/字段/选项值/函数/角色
        final Pair<List<String>, List<String>> pair = relationErpShardService.addDownstream(tenantId, groupId, group.getTemplateId(), group.getDcId(), checkIds);

        return Result.newSuccess(new AddDownstreamEnterprise.Result(pair.getKey(), pair.getValue()));
    }

    @ApiOperation(value = "添加代管下游企业")
    @PostMapping(value = "/addDownstreamEnterprise")
    public DeferredResult<Result<AddDownstreamEnterprise.Result>> addDownstreamEnterprise(@RequestBody AddDownstreamEnterprise.Arg arg) {
        final String tenantId = getLoginUserTenantId();
        final String lang = getLang();

        String actionName = i18NStringManager.get(I18NStringEnum.s3601, lang, getLoginUserTenantId());
        return asyncExecute(() -> addDownstreamEnterprise(arg.getId(), tenantId, arg.getDownstreamAccounts()), 10, false, actionName, lang);
    }

    private Result<AddDownstreamEnterprise.Result> addDownstreamEnterprise(String groupId, String tenantId, List<String> downstreamAccounts) {
        final List<String> eas = downstreamAccounts.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        final List<String> downstreamIds = userCenterService.getTenantIds(eas);

        if (CollectionUtils.isEmpty(downstreamIds)) {
            return Result.newSuccess(new AddDownstreamEnterprise.Result(new ArrayList<>(), new ArrayList<>()));
        }

        final RelationManageGroupEntity group = relationManageGroupDao.getById(tenantId, groupId);
        if (Objects.isNull(group)) {
            return Result.newError(ResultCodeEnum.NOT_FOUND_GROUP);
        }

        // 检查没有任务在处理
        final ManageGroupAddTaskEntity task = manageGroupAddTaskDao.queryByGroupId(tenantId, groupId);
        if (Objects.nonNull(task) && Objects.equals(task.getStatus(), ManageGroupAddTaskEntity.STATUS_INIT)) {
            return Result.newError(ResultCodeEnum.MANAGE_GROUP_ADD_TASK_IS_RUNNING);
        }

        // 只添加不在代管列表中的企业,后期如果取消代管状态可以添加回来的话,这块代码需要修改
        final List<String> checkIds = downstreamIds.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        final List<RelationErpShardEntity> relationErpShardEntities = relationErpShardDao.queryByGroupIdAndDownstreamIds(groupId, checkIds, null);
        relationErpShardEntities.stream().map(RelationErpShardEntity::getDownstreamId).forEach(checkIds::remove);

        if (CollectionUtils.isEmpty(checkIds)) {
            return Result.newSuccess(new AddDownstreamEnterprise.Result(new ArrayList<>(), new ArrayList<>()));
        }

        // 校验下游企业是否符合条件
        final Result<Void> checkResult = relationErpShardService.checkAddDownstreamIds(tenantId, checkIds, group.getTemplateId());
        if (!checkResult.isSuccess()) {
            return Result.copy(checkResult);
        }

        final Pair<List<String>, List<String>> pair = relationErpShardService.addDownstream(tenantId, groupId, group.getTemplateId(), group.getDcId(), checkIds);

        return Result.newSuccess(new AddDownstreamEnterprise.Result(pair.getKey(), pair.getValue()));
    }

    @ApiOperation(value = "查看代管添加下游失败企业")
    @PostMapping(value = "/getManageGroupAddFailRecode")
    public Result<GetManageGroupAddFailRecode.Result> getManageGroupAddFailRecode(@RequestBody GetManageGroupAddFailRecode.Arg arg) {
        final String tenantId = getLoginUserTenantId();

        final RelationManageGroupEntity group = relationManageGroupDao.getById(tenantId, arg.getId());
        if (Objects.isNull(group)) {
            return Result.newError(ResultCodeEnum.NOT_FOUND_GROUP);
        }

        final GetManageGroupAddFailRecode.Result data = new GetManageGroupAddFailRecode.Result();
        final ManageGroupAddTaskEntity task = manageGroupAddTaskDao.queryByGroupId(tenantId, arg.getId());
        if (Objects.isNull(task)) {
            data.setTotal(0);
            data.setDataList(new ArrayList<>());
            return Result.newSuccess(data);
        }

        if (Objects.equals(task.getStatus(), ManageGroupAddTaskEntity.STATUS_INIT)) {
            return Result.newError(ResultCodeEnum.MANAGE_GROUP_ADD_TASK_IS_RUNNING);
        }

        final List<ManageGroupAddDownstreamFailEntity> allByTaskId = manageGroupAddDownstreamFailDao.findAllByTaskId(task.getId(), arg.getQueryStr(), (arg.getPageNum() - 1) * arg.getPageSize(), arg.getPageSize());
        final int count = manageGroupAddDownstreamFailDao.countByTaskId(task.getId(), arg.getQueryStr());
        final List<ManageGroupAddFailRecode> manageGroupAddFailRecodes = convert2ManageGroupAddFailRecode(group.getTemplateId(), allByTaskId);

        data.setTotal(count);
        data.setDataList(manageGroupAddFailRecodes);
        return Result.newSuccess(data);
    }

    @ApiOperation(value = "查看添加下游企业任务")
    @PostMapping(value = "/getAddDownstreamTask")
    public Result<GetAddDownstreamTask.Result> getAddDownstreamTask(@RequestBody GetAddDownstreamTask.Arg arg) {
        final String tenantId = getLoginUserTenantId();

        final String groupId = arg.getId();
        final RelationManageGroupEntity group = relationManageGroupDao.getById(tenantId, groupId);
        if (Objects.isNull(group)) {
            return Result.newError(ResultCodeEnum.NOT_FOUND_GROUP);
        }
        // 检查是否有未完成的任务
        final ManageGroupAddTaskEntity task = manageGroupAddTaskDao.queryByGroupId(tenantId, groupId);
        Integer status = Objects.isNull(task) || Objects.equals(ManageGroupAddTaskEntity.STATUS_END, task.getStatus()) ? 1 : 2;
        final GetAddDownstreamTask.Result result = new GetAddDownstreamTask.Result();
        result.setStatus(status);
        final int total = task.getDownstreamIds().size();
        result.setTotal(total);
        if (Objects.equals(status, 2)) {
            return Result.newSuccess(result);
        }
        final int failedCount = manageGroupAddDownstreamFailDao.countByTaskId(task.getId(), null);
        result.setFail(failedCount);
        result.setSuccess(total - failedCount);
        return Result.newSuccess(result);
    }

    @ApiOperation(value = "查看代管下游企业")
    @PostMapping(value = "/getDownstreamEnterprise")
    public Result<GetDownstreamEnterprise.Result> getDownstreamEnterprise(@RequestBody GetDownstreamEnterprise.Arg arg) {
        final String tenantId = getLoginUserTenantId();

        final String groupId = arg.getId();
        final RelationManageGroupEntity group = relationManageGroupDao.getById(tenantId, groupId);
        if (Objects.isNull(group)) {
            return Result.newError(ResultCodeEnum.NOT_FOUND_GROUP);
        }
        final GetDownstreamEnterprise.Result data = new GetDownstreamEnterprise.Result();

        final int offset = (arg.getPageNum() - 1) * arg.getPageSize();
        if (StringUtils.isBlank(arg.getQueryStr())) {
            final List<RelationErpShardEntity> relationErpShardEntities = relationErpShardDao.pageByStatusAndTenantId(tenantId, groupId, arg.getStatus(), offset, arg.getPageSize());
            final List<DownstreamRelationManage> downstream = convert2DownstreamRelationManage(relationErpShardEntities);
            final int count = relationErpShardDao.countByStatusAndTenantId(tenantId, groupId, arg.getStatus());
            data.setTotal(count);
            data.setDataList(downstream);
        } else {
            final Pair<List<RelationErpShardEntity>, Integer> pair = queryRelationErpShardEntitiesByName(arg.getQueryStr(), groupId, offset, arg.getPageSize());
            data.setDataList(convert2DownstreamRelationManage(pair.getKey()));
            data.setTotal(pair.getValue());
        }
        // 填充概览信息
        return Result.newSuccess(data);
    }

    @NotNull
    private Pair<List<RelationErpShardEntity>, Integer> queryRelationErpShardEntitiesByName(String queryStr, String groupId, int offset, Integer pageSize) {
        final List<String> allDownstreamIdsByGroupId = relationErpShardDao.getAllDownstreamIdsByGroupId(groupId, null);

        // 分批处理,防止oom,但会增加rpc次数
        List<String> tenantIds = new ArrayList<>();
        int count = 0;
        final List<List<String>> partition = ListUtils.partition(allDownstreamIdsByGroupId, 100);
        for (int i = 0; i < partition.size(); i++) {
            final List<String> downstreamIds = partition.get(i);
            final Map<String, String> enterpriseNameMap = userCenterService.batchGetEnterpriseName(downstreamIds);
            final Set<String> collect = enterpriseNameMap.entrySet().stream()
                    .filter(entry -> entry.getValue().contains(queryStr))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toSet());
            for (String ei : collect) {
                if (++count > offset && tenantIds.size() < pageSize) {
                    tenantIds.add(ei);
                }
            }
        }

        final List<RelationErpShardEntity> relationErpShardEntities = CollectionUtils.isNotEmpty(tenantIds) ?
                relationErpShardDao.queryByGroupIdAndDownstreamIds(groupId, tenantIds, null) :
                new ArrayList<>();
        return Pair.of(relationErpShardEntities, count);
    }


    @ApiOperation(value = "获取统计进度")
    @PostMapping(value = "/getDownstreamStatProgress")
    public Result<StreamStatProgress> getDownstreamStatProgress(@RequestBody GetDownstreamEnterprise.StatArg arg) {
        String tenantId = getLoginUserTenantId();
        Result<StreamStatProgress> downstreamStatProgress = relationErpShardService.getDownstreamStatProgress(tenantId, arg.getId());
        return downstreamStatProgress;
    }

    @ApiOperation(value = "刷新下游统计信息，当有任务进行时，将返回当前进度")
    @PostMapping(value = "/refreshDownstreamEnterpriseStat")
    public DeferredResult<Result<StreamStatProgress>> refreshDownstreamEnterpriseStat(@RequestBody GetDownstreamEnterprise.StatArg arg) {
        String tenantId = getLoginUserTenantId();
        DeferredResult<Result<StreamStatProgress>> deferredResult1 = asyncExecuteWithTimeoutResultSup("refreshDownstreamEnterpriseStat", 3,
                deferredResult -> {
                    Result<StreamStatProgress> streamStatProgressResult = relationErpShardService.refreshDownstreamStatTask(tenantId, arg.getId());
                    deferredResult.setResult(streamStatProgressResult);
                },
                () -> {
                    Result<StreamStatProgress> downstreamStatProgress = relationErpShardService.getDownstreamStatProgress(tenantId, arg.getId());
                    return downstreamStatProgress;
                });
        return deferredResult1;
    }


    @ApiOperation(value = "查看代管下游企业统计信息")
    @PostMapping(value = "/getDownstreamEnterpriseStat")
    public Result<GetDownstreamEnterprise.StatResult> getDownstreamEnterpriseStat(@RequestBody GetDownstreamEnterprise.StatArg arg) {
        String tenantId = getLoginUserTenantId();
        Result<GetDownstreamEnterprise.StatResult> downstreamEnterpriseStat = relationErpShardService.getDownstreamEnterpriseStat(tenantId, arg);
        return downstreamEnterpriseStat;
    }

    @ApiOperation(value = "重新同步异常数据")
    @PostMapping(value = "/retryFailedData")
    public DeferredResult<Result<RedoSyncRecord>> retryFailedData(@RequestBody GetDownstreamEnterprise.ResyncArg arg) {
        String tenantId = getLoginUserTenantId();
        String actionName = I18NStringEnum.s3315.getNameByTraceLocale();
        return asyncExecuteWithTimeOutResult(
                actionName,
                deferredResult -> deferredResult.setResult(relationErpShardService.retryFailedData(tenantId, arg.getId(), arg.getDownstreamIds())),
                3,
                defaultTimeoutResult());
    }


    private List<DownstreamRelationManage> convert2DownstreamRelationManage(List<RelationErpShardEntity> relationErpShardEntities) {
        if (CollectionUtils.isEmpty(relationErpShardEntities)) {
            return new ArrayList<>();
        }

        final List<String> collect = relationErpShardEntities.stream()
                .map(RelationErpShardEntity::getDownstreamId)
                .distinct()
                .collect(Collectors.toList());
        final Map<String, SimpleEnterpriseData> enterpriseMap = userCenterService.batchGetSimpleEnterprise(collect);

        return relationErpShardEntities.stream()
                .map(entity -> {
                    final SimpleEnterpriseData simpleEnterpriseData = enterpriseMap.get(entity.getDownstreamId());
                    return DownstreamRelationManage.builder()
                            .tenantId(entity.getDownstreamId())
                            .enterpriseAccount(simpleEnterpriseData.getEnterpriseAccount())
                            .enterpriseName(simpleEnterpriseData.getEnterpriseName())
                            .status(entity.getStatus())
                            .build();
                }).collect(Collectors.toList());
    }

    @NotNull
    private List<ManageGroupAddFailRecode> convert2ManageGroupAddFailRecode(String templateId, List<ManageGroupAddDownstreamFailEntity> relationErpShardEntities) {
        if (CollectionUtils.isEmpty(relationErpShardEntities)) {
            return new ArrayList<>();
        }

        final List<String> collect = relationErpShardEntities.stream()
                .map(ManageGroupAddDownstreamFailEntity::getDownstreamId)
                .distinct()
                .collect(Collectors.toList());
        final Map<String, SimpleEnterpriseData> enterpriseMap = userCenterService.batchGetSimpleEnterprise(collect);

        final List<String> lackObject = relationErpShardEntities.stream()
                .map(ManageGroupAddDownstreamFailEntity::getCheckResult)
                .map(CheckResultEntity::getLackObject)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        final Map<String, String> objectNames = crmRemoteManager.listObjectNamesByApiNames(templateId, lackObject);

        final List<String> lackFieldsObject = Stream.concat(
                        relationErpShardEntities.stream()
                                .map(ManageGroupAddDownstreamFailEntity::getCheckResult)
                                .map(CheckResultEntity::getLackField)
                                .flatMap(Collection::stream)
                                .map(CheckResultEntity.Field::getObjectApiName),
                        relationErpShardEntities.stream()
                                .map(ManageGroupAddDownstreamFailEntity::getCheckResult)
                                .map(CheckResultEntity::getLackOption)
                                .flatMap(Collection::stream)
                                .map(CheckResultEntity.Option::getObjectApiName)
                ).distinct()
                .collect(Collectors.toList());
        final List<ObjectDescribe> objectDescribes = crmRemoteManager.listObjectAndFieldByApiNames(templateId, lackFieldsObject);
        final Map<String, ObjectDescribe> describeMap = objectDescribes.stream().collect(Collectors.toMap(ObjectDescribe::getApiName, Function.identity()));

        final List<String> lackRole = relationErpShardEntities.stream()
                .map(ManageGroupAddDownstreamFailEntity::getCheckResult)
                .map(CheckResultEntity::getLackRole)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        final Map<String, String> roleNameMap = userRoleManager.queryRoleNameByRoleCode(templateId, lackRole);

        return relationErpShardEntities.stream()
                .map(entity -> convert2ManageGroupAddFailRecode(entity, enterpriseMap, objectNames, describeMap, roleNameMap))
                .collect(Collectors.toList());
    }

    @NotNull
    private static ManageGroupAddFailRecode convert2ManageGroupAddFailRecode(ManageGroupAddDownstreamFailEntity entity, Map<String, SimpleEnterpriseData> enterpriseMap, Map<String, String> objectNames, Map<String, ObjectDescribe> describeMap, Map<String, String> roleNameMap) {
        final ManageGroupAddFailRecode manageGroupAddFailRecode = new ManageGroupAddFailRecode();
        final String downStreamId = entity.getDownstreamId();
        manageGroupAddFailRecode.setTenantId(downStreamId);
        final SimpleEnterpriseData simpleEnterpriseData = enterpriseMap.get(downStreamId);
        manageGroupAddFailRecode.setEnterpriseAccount(simpleEnterpriseData.getEnterpriseAccount());
        manageGroupAddFailRecode.setEnterpriseName(simpleEnterpriseData.getEnterpriseName());

        final CheckResultEntity resultEntity = entity.getCheckResult();
        final ManageGroupAddFailRecode.CheckResult checkResult = new ManageGroupAddFailRecode.CheckResult();
        checkResult.setLackObject(convert2LackInfo(resultEntity.getLackObject(), objectNames));
        checkResult.setLackRole(convert2LackInfo(resultEntity.getLackRole(), roleNameMap));
        checkResult.setLackFunc(convert2LackInfo(resultEntity.getLackFunc(), resultEntity.getLackFunc().stream().collect(Collectors.toMap(Function.identity(), Function.identity()))));

        final List<ManageGroupAddFailRecode.FieldInfo> fieldInfos = resultEntity.getLackField().stream()
                .collect(Collectors.groupingBy(CheckResultEntity.Field::getObjectApiName))
                .values().stream()
                .filter(CollectionUtils::isNotEmpty)
                .map(fields -> {
                    final String objectApiName = fields.get(0).getObjectApiName();
                    final ObjectDescribe objectDescribe = describeMap.get(objectApiName);
                    final List<ManageGroupAddFailRecode.LackInfo> lackInfos = fields.stream()
                            .map(CheckResultEntity.Field::getFieldApiName)
                            .map(fieldApiName -> {
                                final String label = Optional.ofNullable(objectDescribe)
                                        .map(ObjectDescribe::getFields)
                                        .map(map -> map.get(fieldApiName))
                                        .map(fieldDescribe -> fieldDescribe.getLabel())
                                        .orElse(null);
                                return new ManageGroupAddFailRecode.LackInfo(fieldApiName, label);
                            }).collect(Collectors.toList());

                    final String displayName = Optional.ofNullable(objectDescribe).map(ObjectDescribe::getDisplayName).orElse(null);
                    return new ManageGroupAddFailRecode.FieldInfo(objectApiName, displayName, lackInfos);
                }).collect(Collectors.toList());
        checkResult.setLackField(fieldInfos);

        final List<ManageGroupAddFailRecode.FieldInfo> lackOptionFieldInfos = resultEntity.getLackOption().stream()
                .collect(Collectors.groupingBy(CheckResultEntity.Option::getObjectApiName))
                .values().stream()
                .filter(CollectionUtils::isNotEmpty)
                .map(options -> {
                    final String objectApiName = options.get(0).getObjectApiName();
                    final ObjectDescribe objectDescribe = describeMap.get(objectApiName);
                    final List<ManageGroupAddFailRecode.LackInfo> lackInfos = options.stream()
                            .map(CheckResultEntity.Option::getFieldApiName)
                            .distinct()
                            .map(fieldApiName -> {
                                final String label = Optional.ofNullable(objectDescribe)
                                        .map(ObjectDescribe::getFields)
                                        .map(map -> map.get(fieldApiName))
                                        .map(fieldDescribe -> fieldDescribe.getLabel())
                                        .orElse(null);
                                return new ManageGroupAddFailRecode.LackInfo(fieldApiName, label);
                            }).collect(Collectors.toList());

                    final String displayName = Optional.ofNullable(objectDescribe).map(ObjectDescribe::getDisplayName).orElse(null);
                    return new ManageGroupAddFailRecode.FieldInfo(objectApiName, displayName, lackInfos);
                }).collect(Collectors.toList());
        checkResult.setLackOptionField(lackOptionFieldInfos);

        manageGroupAddFailRecode.setCheckResult(checkResult);

        return manageGroupAddFailRecode;
    }

    public static List<ManageGroupAddFailRecode.LackInfo> convert2LackInfo(List<String> ids, Map<String, String> nameMap) {
        return ids.stream()
                .map(id -> new ManageGroupAddFailRecode.LackInfo(id, nameMap.get(id)))
                .collect(Collectors.toList());
    }

    @ApiOperation(value = "删除代管企业")
    @PostMapping(value = "/deleteDownstreamEnterprise")
    public Result<DeleteDownstreamEnterprise.Result> deleteDownstreamEnterprise(@RequestBody DeleteDownstreamEnterprise.Arg arg) {
        final String tenantId = getLoginUserTenantId();

        final RelationManageGroupEntity group = relationManageGroupDao.getById(tenantId, arg.getId());
        if (Objects.isNull(group)) {
            return Result.newError(ResultCodeEnum.NOT_FOUND_GROUP);
        }

        if (BooleanUtils.isNotTrue(arg.getAll()) && CollectionUtils.isEmpty(arg.getDownstreamIds())) {
            return Result.newSuccess(new DeleteDownstreamEnterprise.Result());
        }

        if (BooleanUtils.isTrue(arg.getAll())) {
            relationErpShardService.deleteAllDownstreamEnterprise(group.getId(), group.getTemplateId(), group.getDcId());
        } else {
            relationErpShardService.deleteDownstreamEnterprise(group.getId(), group.getTemplateId(), group.getDcId(), arg.getDownstreamIds());
        }

        return Result.newSuccess(new DeleteDownstreamEnterprise.Result());
    }

    @ApiOperation(value = "删除代管企业组")
    @PostMapping(value = "/deleteRelationManage")
    public Result<DeleteRelationManage.Result> deleteRelationManage(@RequestBody DeleteRelationManage.Arg arg) {
        final String tenantId = getLoginUserTenantId();

        final RelationManageGroupEntity group = relationManageGroupDao.getById(tenantId, arg.getId());
        if (Objects.isNull(group)) {
            return Result.newError(ResultCodeEnum.NOT_FOUND_GROUP);
        }

        relationErpShardService.deleteAllDownstreamEnterprise(group.getId(), group.getTemplateId(), group.getDcId());

        relationManageGroupDao.deleteByTenantIdAndId(tenantId, arg.getId());

        return Result.newSuccess(new DeleteRelationManage.Result());
    }
}
