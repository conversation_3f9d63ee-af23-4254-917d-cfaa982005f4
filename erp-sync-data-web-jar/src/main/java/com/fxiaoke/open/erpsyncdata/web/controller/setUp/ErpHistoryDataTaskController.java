package com.fxiaoke.open.erpsyncdata.web.controller.setUp;


import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistorySnapshotResult;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpHistoryDataTaskService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 20:27 2021/8/10
 * @Desc:
 */
@Slf4j
@Api(tags = "erp历史数据同步任务相关接口")
@RestController("erpHistoryDataTaskController")
@RequestMapping("cep/setUp/erpHistoryDataTask")
public class ErpHistoryDataTaskController extends BaseController {
    @Autowired
    private ErpHistoryDataTaskService erpHistoryDataTaskService;

    @ApiOperation(value = "创建同步任务")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public Result<String> createErpHistoryDataTask(@RequestBody CreateErpHistoryDataTaskArg arg,
                                                   @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dcId=getDcId();
        if(StringUtils.isNotBlank(arg.getTask().getDataCenterId())){
            dcId=arg.getTask().getDataCenterId();
        }
        arg.getTask().setCreator(userId);
        return erpHistoryDataTaskService.createErpHistoryDataTask(tenantId,dcId, userId, arg,lang);
    }
    @ApiOperation(value = "编辑同步任务")
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    public Result<String> editErpHistoryDataTask(@RequestBody CreateErpHistoryDataTaskArg arg,
                                                   @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dcId=getDcId();
        if(StringUtils.isNotBlank(arg.getTask().getDataCenterId())){
            dcId=arg.getTask().getDataCenterId();
        }
        arg.getTask().setCreator(userId);
        return erpHistoryDataTaskService.editErpHistoryDataTask(tenantId,dcId, userId, arg,lang);
    }

    @ApiOperation(value = "获取同步任务列表")
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public Result<QueryResult<List<ErpHistoryDataTaskResult>>> queryErpHistoryDataTask(@RequestBody QueryErpHistoryDataTasksArg arg,
                                                                                       @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpHistoryDataTaskService.queryErpHistoryDataTask(tenantId, userId, arg,lang);
    }

    @ApiOperation(value = "任务详情")
    @RequestMapping(value = "/getTaskDetail", method = RequestMethod.POST)
    public Result<ErpHistoryDataTaskResult> getTaskDetail(@RequestBody BaseTaskArg arg,
                                                                                       @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpHistoryDataTaskService.queryErpHistoryDetail(tenantId,userId,arg,lang);
    }

    @ApiOperation(value = "返回列表任务日志")
    @RequestMapping(value = "/listByTaskId", method = RequestMethod.POST)
    public  Result<QueryResult<List<ErpHistorySnapshotResult>>> listByTaskId(@RequestBody QueryErpHistoryDataTasksArg arg,
                                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpHistoryDataTaskService.queryErpTaskSnapshotList(tenantId,arg.getDataCenterId(),userId,arg,lang);

    }

    /**
     *
     * @param deleteArg
     * @return
     */
    @ApiOperation(value = "删除同步任务")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public Result<String> deleteErpHistoryDataTask(@RequestBody BaseTaskArg deleteArg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();

        return erpHistoryDataTaskService.deleteErpHistoryDataTask(tenantId, userId, deleteArg);
    }

    /**
     * 前端未使用
     * @param idArg
     * @return
     */
    @ApiOperation(value = "获取单条同步任务详情")
    @RequestMapping(value = "/get", method = RequestMethod.POST)
    public Result<ErpHistoryDataTaskResult> getErpHistoryDataTask(@RequestBody BaseArg idArg,
                                                                  @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpHistoryDataTaskService.getErpHistoryDataTask(tenantId, userId, idArg,lang);
    }

    @ApiOperation(value = "中断同步任务")
    @RequestMapping(value = "/stop", method = RequestMethod.POST)
    public Result<String> stopErpHistoryDataTask(@RequestBody BaseTaskArg idArg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();

        return erpHistoryDataTaskService.stopErpHistoryDataTask(tenantId,userId, idArg);
    }
    @ApiOperation(value = "重启同步任务")
    @RequestMapping(value = "/reStart", method = RequestMethod.POST)
    public Result<String> reStartErpHistoryDataTask(@RequestBody ErpHistoryDataRestartTasksArg erpHistoryDataRestartTasksArg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpHistoryDataTaskService.reStartErpHistoryDataTask(tenantId, userId, erpHistoryDataRestartTasksArg);

    }


}
