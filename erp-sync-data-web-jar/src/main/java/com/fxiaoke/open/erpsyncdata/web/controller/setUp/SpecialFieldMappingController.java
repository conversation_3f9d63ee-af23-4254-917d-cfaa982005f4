package com.fxiaoke.open.erpsyncdata.web.controller.setUp;


import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.fxiaoke.erpdss.connector.core.model.ConnectorFeature;
import com.fxiaoke.open.erpsyncdata.admin.arg.CepArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.CepListArg;
import com.fxiaoke.open.erpsyncdata.admin.manager.ExcelListener.FieldDataMappingListener;
import com.fxiaoke.open.erpsyncdata.admin.model.BatchExportSystemFieldMapping;
import com.fxiaoke.open.erpsyncdata.admin.model.ExportDistrictMapping;
import com.fxiaoke.open.erpsyncdata.admin.model.GetSystemFieldMappingExcelTemplate;
import com.fxiaoke.open.erpsyncdata.admin.model.ImportSystemFieldMapping;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.FieldDataMappingExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.remote.StoneFileManager;
import com.fxiaoke.open.erpsyncdata.admin.service.ConnectInfoService;
import com.fxiaoke.open.erpsyncdata.admin.service.SpecialFieldMappingService;
import com.fxiaoke.open.erpsyncdata.admin.service.SystemFieldService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ConnectorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DBFileManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ExcelSheetArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BaseArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryFieldDataBindingArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SystemFieldMappingArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ExcelSystemFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.FieldTypeMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.SpecialFieldMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date: 10:27 2020/8/18
 * @Desc:
 */
@Slf4j
@Api(tags = "特殊类型字段映射设置相关接口")
@RestController("setUpSpecialFieldMappingController")
@RequestMapping("cep/setUp/specialFieldMapping")
public class SpecialFieldMappingController extends AsyncSupportController {
    @Autowired
    private SpecialFieldMappingService specialFieldMappingService;
    @Autowired
    private ConnectInfoService connectInfoService;
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;

    @Autowired
    private SystemFieldService systemFieldService;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private DBFileManager dbFileManager;
    @Autowired
    private StoneFileManager stoneFileManager;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private ConnectorManager connectorManager;

    @ApiOperation(value = "获取erp字段映射信息")
    @RequestMapping(value = "/querySpecialFieldMapping", method = RequestMethod.POST)
    public Result<QueryResult<List<SpecialFieldMappingResult>>> queryErpObject(@RequestBody QueryFieldDataBindingArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return specialFieldMappingService.querySpecialFieldMapping(tenantId, userId, getDcId(), arg);
    }

    @ApiOperation(value = "更新或者新增erp字段映射信息")
    @RequestMapping(value = "/updateSpecialFieldMapping", method = RequestMethod.POST)
    public Result<String> updateErpObject(@RequestBody CepListArg<SpecialFieldMappingResult> arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return specialFieldMappingService.updateSpecialFieldMappings(tenantId, userId, getDcId(), arg.getCepListArg());
    }

    @ApiOperation(value = "删除erp字段映射信息")
    @RequestMapping(value = "/deleteSpecialFieldMapping", method = RequestMethod.POST)
    public Result<String> deleteSpecialFieldMapping(@RequestBody BaseArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return specialFieldMappingService.deleteSpecialFieldMapping(tenantId, userId, arg);
    }

    @ApiOperation(value = "批量删除erp字段映射信息")
    @RequestMapping(value = "/batchDeleteSystemFieldMapping", method = RequestMethod.POST)
    public Result<Long> batchDeleteSpecialFieldMapping(@RequestBody SystemFieldMappingArg arg,
                                                       @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return systemFieldService.batchDeleteFieldMapping(tenantId,userId,getDcId(),arg.getFieldType(),arg.getIdList(),lang);
    }

    @ApiOperation(value = "导出系统字段")
    @RequestMapping(value = "/exportSystemFieldMapping", method = RequestMethod.POST)
    public DeferredResult<Result<Void>> exportSystemFieldMapping(@RequestBody SystemFieldMappingArg arg,
                                                                 @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = getDcId();
        DeferredResult<Result<Void>> result = new DeferredResult<>(1000 * 5L, Result.newSuccess(i18NStringManager.get(I18NStringEnum.s402,lang,tenantId)));
        ParallelUtils.createBackgroundTask().submit(() -> {
            Result<Void> result1 = systemFieldService.exportSystemFieldMapping(tenantId,
                    userId,
                    dataCenterId,
                    arg.getFieldType(),
                    arg.getIdList(),
                    lang);
            result.setResult(result1);
        }).run();
        return result;
    }

    @ApiOperation(value = "自动同步crm/K3c国家省市区映射excel")
    @PostMapping(value = "/exportDistrictMapping")
    public DeferredResult<Result<ExportDistrictMapping.Result>> autoMatchDistrictMapping(@RequestBody ExportDistrictMapping.Arg arg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = getDcId();
        return asyncExecute(() -> autoMatchDistrictMapping(tenantId, userId, dataCenterId, arg, lang), 5, false, i18NStringManager.get(I18NStringEnum.s2044,lang,tenantId), r -> r.isSuccess() ? i18NStringManager.get(I18NStringEnum.s2045,lang,tenantId) + r.getData().getDownloadUrl() : null, Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT), lang);
    }

    private Result<ExportDistrictMapping.Result> autoMatchDistrictMapping(String tenantId, Integer userId, String dataCenterId, ExportDistrictMapping.Arg arg, String lang) {
        final Result<List<ExcelSheetArg>> listResult = systemFieldService.autoMatchDistrictMapping(tenantId,
                userId,
                lang,
                dataCenterId,
                arg.getCountry(),
                arg.getProvince(),
                arg.getCity(),
                arg.getCounty(),
                arg.getTown());
        if (!listResult.isSuccess()) {
            return Result.copy(listResult);
        }

        final List<ExcelSheetArg> sheetArgs = listResult.getData();
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);

        final String now = DateTimeFormatter.ofPattern("yyyyMMdd_HHmm").format(LocalDateTime.now());
        final String fileName = connectInfo.getDataCenterName() + "_" + sheetArgs.stream().map(ExcelSheetArg::getSheetName).collect(Collectors.joining()) + "_" + now;

        // 生成文件
        final Result<String> uploadExcel = dbFileManager.writeAndUploadExcel(tenantId, fileName, sheetArgs, lang);
        if (!uploadExcel.isSuccess()) {
            return Result.copy(uploadExcel);
        }
        final String npath = uploadExcel.getData();
        String url = getDownLoadUrl(tenantId, fileName, npath);

        final ExportDistrictMapping.Result data = new ExportDistrictMapping.Result();
        data.setPath(npath);
        data.setDownloadUrl(url);

        return Result.newSuccess(data);
    }

    private String getDownLoadUrl(String tenantId, String fileName, String npath) {
        // 中文编码,防止企信解析不出来url
        String encodedText;
        try {
            encodedText = URLEncoder.encode(fileName, "UTF-8");
        } catch (Exception e) {
            log.warn("编码失败 fileName:{}", fileName, e);
            encodedText = fileName;
        }
        return String.format(userCenterService.getDownloadFilePath(tenantId), npath, encodedText + ".xlsx");
    }

    @ApiOperation(value = "批量导出多类型映射excel")
    @PostMapping(value = "/batchExportSystemFieldMapping")
    public DeferredResult<Result<BatchExportSystemFieldMapping.Result>> batchExportSystemFieldMapping(@RequestBody BatchExportSystemFieldMapping.Arg arg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId = getDcId();

        return asyncExecute(() -> batchExportSystemFieldMapping(tenantId, userId, dataCenterId, arg, lang), 5, false, i18NStringManager.get(I18NStringEnum.s2043,lang,tenantId), r -> r.isSuccess() ? i18NStringManager.get(I18NStringEnum.s2045,lang,tenantId) + r.getData().getDownloadUrl() : null, Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT), lang);
    }

    private Result<BatchExportSystemFieldMapping.Result> batchExportSystemFieldMapping(String tenantId, Integer userId, String dataCenterId, BatchExportSystemFieldMapping.Arg arg, String lang) {
        final Result<List<ExcelSheetArg>> listResult = systemFieldService.batchExportSystemFieldMapping(tenantId, userId, dataCenterId, arg.getFieldType(), arg.getIdList(), lang);
        if (!listResult.isSuccess()) {
            return Result.copy(listResult);
        }

        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        final String name = listResult.getData().stream().map(ExcelSheetArg::getSheetName).collect(Collectors.joining());

        final String now = DateTimeFormatter.ofPattern("yyyyMMdd_HHmm").format(LocalDateTime.now());
        final String fileName = connectInfo.getDataCenterName() + "_" + name + i18NStringManager.get(I18NStringEnum.s2073,lang,tenantId) + "_" + now;

        // 生成文件
        final Result<String> uploadExcel = dbFileManager.writeAndUploadExcel(tenantId, fileName, listResult.getData(), lang);
        if (!uploadExcel.isSuccess()) {
            return Result.copy(uploadExcel);
        }
        final String npath = uploadExcel.getData();
        String url = getDownLoadUrl(tenantId, fileName, npath);

        final BatchExportSystemFieldMapping.Result data = new BatchExportSystemFieldMapping.Result();
        data.setPath(npath);
        data.setDownloadUrl(url);

        return Result.newSuccess(data);
    }

    @ApiOperation(value = "导入系统字段映射excel")
    @PostMapping(value = "/importSystemFieldMapping")
    public DeferredResult<Result<ImportSystemFieldMapping.Result>> importSystemFieldMapping(@RequestBody ImportSystemFieldMapping.Arg arg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        return asyncExecute(() -> {
            ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
            try (InputStream inputStream = stoneFileManager.downloadByPath(tenantId, arg.getNpath(), "xlsx")) {

                final List<ImportSystemFieldMapping.SheetResult> sheetResults = importSheetResults(tenantId, dataCenterId, connectInfo, inputStream, lang);

                final String collect = sheetResults.stream()
                        .map(sheetResult -> sheetResult.getSheetName() + i18NStringManager.get2(I18NStringEnum.s225, lang, tenantId, sheetResult.getResult().getPrintMsg()))
                        .map(msg -> msg.replace("\n", "\n\t"))
                        .collect(Collectors.joining("\n"));
                return Result.newSuccess(new ImportSystemFieldMapping.Result(sheetResults, collect));
            } catch (Exception e) {
                log.error("importDistrictMapping error, tenantId:{} npath:{}", tenantId, arg.getNpath(), e);
                return Result.newError(i18NStringManager.get(I18NStringEnum.s2072, lang, tenantId) + e.getMessage());
            }
        }, 5, false, i18NStringManager.get(I18NStringEnum.s2042, lang, tenantId), r -> r.isSuccess() ? r.getData().getSheetResults().stream().map(sheetResult -> sheetResult.getSheetName() + i18NStringManager.get2(I18NStringEnum.s225, lang, tenantId, sheetResult.getResult().getPrintMsg())).collect(Collectors.joining("\n")) : null, Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT), lang);
    }

    private List<ImportSystemFieldMapping.SheetResult> importSheetResults(String tenantId, String dataCenterId, ErpConnectInfoEntity connectInfo, InputStream inputStream, String lang) {
        final ExcelReader build = EasyExcelFactory.read(inputStream).excelType(ExcelTypeEnum.XLSX).build();
        List<ReadSheet> sheetList = build.excelExecutor().sheetList();
        final Map<String, ExcelSystemFieldTypeEnum> districtEnumMap = Arrays.stream(ExcelSystemFieldTypeEnum.values())
                .collect(Collectors.toMap(
                        systemFieldTypeEnum -> i18NStringManager.get(systemFieldTypeEnum.getI18nEnum(), lang, tenantId),
                        Function.identity(),
                        (o1, o2) -> o2));

        List<Pair<String, FieldDataMappingListener>> collect = sheetList.stream()
                .map(readSheet -> {
                    final String sheetName = readSheet.getSheetName();
                    final ExcelSystemFieldTypeEnum excelSystemFieldTypeEnum = districtEnumMap.get(sheetName);
                    if (excelSystemFieldTypeEnum == null) {
                        return null;
                    }
                    final ErpFieldTypeEnum erpFieldTypeEnum = excelSystemFieldTypeEnum.getErpFieldTypeEnum();
                    readSheet.setClazz(FieldDataMappingExcelVo.class);
                    final FieldDataMappingListener listener = new FieldDataMappingListener(tenantId, connectInfo.getChannel(), erpFieldTypeEnum, erpFieldDataMappingDao, idGenerator, dataCenterId, i18NStringManager, lang);
                    readSheet.setCustomReadListenerList(Collections.singletonList(listener));
                    return Pair.of(sheetName, listener);
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
        build.readAll();

        return collect.stream()
                .map(pair -> new ImportSystemFieldMapping.SheetResult(pair.getKey(), pair.getValue().getImportResult()))
                .collect(Collectors.toList());
    }

    @ApiOperation(value = "获取导入模板")
    @PostMapping( "/getSystemFieldMappingExcelTemplate")
    public Result<GetSystemFieldMappingExcelTemplate.Result> getSystemFieldMappingExcelTemplate(@RequestBody GetSystemFieldMappingExcelTemplate.Arg arg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();

        final Result<List<ExcelSheetArg>> listResult = systemFieldService.getSystemFieldMappingExcelTemplate(tenantId, dataCenterId, arg.getFieldType(), lang);
        if (!listResult.isSuccess()) {
            return Result.copy(listResult);
        }

        final String name = listResult.getData().stream().map(ExcelSheetArg::getSheetName).collect(Collectors.joining());
        final String fileName = name + i18NStringManager.get(I18NStringEnum.s2074, lang, tenantId);
        // 生成文件
        final Result<String> uploadExcel = dbFileManager.writeAndUploadExcel(tenantId, fileName, listResult.getData(), lang);
        if (!uploadExcel.isSuccess()) {
            return Result.copy(uploadExcel);
        }
        final String npath = uploadExcel.getData();
        String url = getDownLoadUrl(tenantId, fileName, npath);

        final GetSystemFieldMappingExcelTemplate.Result data = new GetSystemFieldMappingExcelTemplate.Result();
        data.setPath(npath);
        data.setDownloadUrl(url);

        return Result.newSuccess(data);
    }

    @ApiOperation(value = "支持的特殊字段类型")
    @RequestMapping(value = "/queryFieldType", method = RequestMethod.POST)
    public Result<List<FieldTypeMappingResult>> queryFieldType(@RequestBody CepArg arg,
                                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        log.info("SpecialFieldMappingController.queryFieldType,lang={}",lang);
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        Result<ConnectInfoResult> connectInfo = connectInfoService.getConnectInfoByDataCenterId(tenantId, userId, getDcId());

        ErpChannelEnum erpChannelEnum = connectInfo.getData().getChannel();

        List<FieldTypeMappingResult> fieldTypeMappingResults = Lists.newArrayList();
        final String fieldType = Stream.of(ErpFieldTypeEnum.country, ErpFieldTypeEnum.province, ErpFieldTypeEnum.city, ErpFieldTypeEnum.district, ErpFieldTypeEnum.town).map(Enum::name).collect(Collectors.joining(","));
        final String fieldTypeLabel = Stream.of(I18NStringEnum.s216, I18NStringEnum.s217, I18NStringEnum.s218, I18NStringEnum.s219, I18NStringEnum.s331)
                .map(i18NStringEnum -> i18NStringManager.get(i18NStringEnum, lang, tenantId))
                .collect(Collectors.joining(","));

        FieldTypeMappingResult countryFieldTypeMapping = new FieldTypeMappingResult();
        countryFieldTypeMapping.setErpFieldType(fieldType);
        countryFieldTypeMapping.setFsFieldType(fieldType);
        countryFieldTypeMapping.setErpFieldTypeLabel(fieldTypeLabel);
        countryFieldTypeMapping.setFsFieldTypeLabel(fieldTypeLabel);
        fieldTypeMappingResults.add(countryFieldTypeMapping);

        FieldTypeMappingResult departmentFieldTypeMapping = new FieldTypeMappingResult();
        departmentFieldTypeMapping.setErpFieldType(ErpFieldTypeEnum.department.name());
        departmentFieldTypeMapping.setFsFieldType(ErpFieldTypeEnum.department.name());
        departmentFieldTypeMapping.setErpFieldTypeLabel(i18NStringManager.get(I18NStringEnum.s221,lang,tenantId));
        departmentFieldTypeMapping.setFsFieldTypeLabel(i18NStringManager.get(I18NStringEnum.s221,lang,tenantId));
        fieldTypeMappingResults.add(departmentFieldTypeMapping);

        Result<Long> firstErpConnectorLicenseTime = getFirstErpConnectorLicenseCreateTime(tenantId);
        log.info("SpecialFieldMappingController.queryFieldType,firstErpConnectorLicenseTime={},HIDE_CATEGORY_ENTRANCE_DATE={}",
                firstErpConnectorLicenseTime, ConfigCenter.HIDE_CATEGORY_ENTRANCE_DATE);

        int count = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .countByTenantIdAndDataTypeAndQueryStr(tenantId, getDcId(), ErpFieldTypeEnum.category, null);

        log.info("SpecialFieldMappingController.queryFieldType,product category data,count={}", count);

        if (firstErpConnectorLicenseTime.isSuccess()) {
            if (firstErpConnectorLicenseTime.getData() < ConfigCenter.HIDE_CATEGORY_ENTRANCE_DATE || count != 0) {
                FieldTypeMappingResult categoryFieldTypeMapping = new FieldTypeMappingResult();
                categoryFieldTypeMapping.setErpFieldType(ErpFieldTypeEnum.category.name());
                categoryFieldTypeMapping.setFsFieldType(ErpFieldTypeEnum.category.name());
                categoryFieldTypeMapping.setErpFieldTypeLabel(i18NStringManager.get(I18NStringEnum.s332,lang,tenantId));
                categoryFieldTypeMapping.setFsFieldTypeLabel(i18NStringManager.get(I18NStringEnum.s333,lang,tenantId));
                fieldTypeMappingResults.add(categoryFieldTypeMapping);
            }
        }

        //查看是否支持文件传输
        Long features = connectorManager.getConnectorFeatures(tenantId, getDcId());
        if (ErpChannelEnum.ERP_K3CLOUD.equals(erpChannelEnum)
                ||ConnectorFeature.supportConvertFileField2CRM.isEnabled(features)
                || ConnectorFeature.supportConvertFileField2Out.isEnabled(features)) {
            FieldTypeMappingResult fileMapping = new FieldTypeMappingResult();
            fileMapping.setErpFieldType(ErpFieldTypeEnum.file_attachment.name());
            fileMapping.setFsFieldType(ErpFieldTypeEnum.file_attachment.name());
            fileMapping.setErpFieldTypeLabel(I18NStringEnum.file.getText());
            fileMapping.setFsFieldTypeLabel(I18NStringEnum.file.getText());
            fieldTypeMappingResults.add(fileMapping);
        }

        for (FieldTypeMappingResult result : fieldTypeMappingResults) {
            result.setChannel(erpChannelEnum);
        }
        return Result.newSuccess(fieldTypeMappingResults);
    }

    /**
     * 获取客户首次开通的ERP连接器的license创建时间
     *
     * @param tenantId
     * @return
     */
    private Result<Long> getFirstErpConnectorLicenseCreateTime(String tenantId) {
        QueryProductArg arg = new QueryProductArg();

        LicenseContext context = new LicenseContext();
        context.setAppId("CRM");
        context.setTenantId(tenantId);
        arg.setLicenseContext(context);

        Set<String> erpConnectorSet = Sets.newHashSet(ErpChannelEnum.ERP_K3CLOUD.getModuleCode(),
                ErpChannelEnum.ERP_SAP.getModuleCode(),
                ErpChannelEnum.STANDARD_CHANNEL.getModuleCode(),
                ErpChannelEnum.ERP_U8.getModuleCode(),
                ErpChannelEnum.ERP_U8_EAI.getModuleCode(),
                ErpChannelEnum.ERP_DB_PROXY.getModuleCode());


        List<ProductVersionPojo> erpProductVersionList = new ArrayList<>();
        LicenseVersionResult result = licenseClient.queryProductVersion(arg);
        log.info("SpecialFieldMappingController.getFirstErpConnectorLicenseCreateTime,result={}", result);
        if (result.getErrCode() == 0 && CollectionUtils.isNotEmpty(result.getResult())) {
            for (ProductVersionPojo productVersionPojo : result.getResult()) {
                if (erpConnectorSet.contains(productVersionPojo.getCurrentVersion())) {
                    erpProductVersionList.add(productVersionPojo);
                }
            }
        }

        Optional<Long> latestCreateTime = erpProductVersionList.stream().map(ProductVersionPojo::getStartTime).sorted().findFirst();
        log.info("SpecialFieldMappingController.getFirstErpConnectorLicenseCreateTime,latestCreateTime={}", latestCreateTime);
        if (latestCreateTime.isPresent() == false)
            return Result.newError(ResultCodeEnum.NO_VALID_ERP_CONNECTOR_LICENSE);
        return Result.newSuccess(latestCreateTime.get());
    }

}
