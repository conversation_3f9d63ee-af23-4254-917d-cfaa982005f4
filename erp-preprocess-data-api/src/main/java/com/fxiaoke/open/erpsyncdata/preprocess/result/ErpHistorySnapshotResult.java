package com.fxiaoke.open.erpsyncdata.preprocess.result;


import cn.hutool.core.lang.ObjectId;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpTaskStatusArgEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 20:35 2021/8/10
 * @Desc: 历史任务的快照
 */
@Data
@ApiModel
public class ErpHistorySnapshotResult implements Serializable {

    /**
     * 在这个时间后才执行
     */
    @ApiModelProperty("执行时间")
    public Long executeTime;
    @ApiModelProperty("任务状态(1.创建，2.开启，3执行中，4.异常，5.中断，6.结束（成功）)")
    public ErpHistoryDataTaskStatusEnum taskStatusEnum; //
    @ApiModelProperty("任务状态描述")
    public String taskStatusDesc; //
    @ApiModelProperty("累计轮询数据")
    public Long totalDataSize; //
    @ApiModelProperty("备注")
    public String remark; //

    @ApiModelProperty("objectid")
    public ObjectId id; //
}
