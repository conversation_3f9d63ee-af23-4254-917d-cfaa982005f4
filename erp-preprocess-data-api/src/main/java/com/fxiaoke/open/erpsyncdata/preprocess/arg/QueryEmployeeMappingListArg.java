package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date: 14:26 2020/11/10
 * @Desc:
 */
@Getter
@Setter
@ToString
@ApiModel("查询员工绑定列表参数")
public class QueryEmployeeMappingListArg extends PageArg {
    @ApiModelProperty("是否已绑定，默认为空")
    private Boolean isBind;
    @ApiModelProperty("ERP字段类型，这里支持employee & user")
    private ErpFieldTypeEnum fieldType;
}
