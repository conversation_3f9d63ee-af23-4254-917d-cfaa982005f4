package com.facishare.open.erp.connertor.sdk.controller;

import com.facishare.open.erp.connertor.sdk.model.GetUserToken;
import com.facishare.open.erp.connertor.sdk.model.RefreshToken;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2023/5/10 17:45:50
 */
public interface IOauthLoginController {
    @RequestMapping("/getUserToken")
    GetUserToken.Result getUserToken(@RequestBody GetUserToken.Arg arg);

    @RequestMapping("/refreshToken")
    RefreshToken.Result refreshToken(@RequestBody RefreshToken.Arg arg);
}
