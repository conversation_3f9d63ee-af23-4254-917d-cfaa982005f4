<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
    <context:annotation-config/>
    <context:component-scan base-package="com.fxiaoke.open.erpsyncdata"/>
    <!-- 异步线程池 -->
    <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数  -->
        <property name="corePoolSize" value="2"/>
        <!-- 最大线程数 -->
        <property name="maxPoolSize" value="32"/>
        <!-- 队列最大长度 >=mainExecutor.maxSize -->
        <property name="queueCapacity" value="1800"/>
        <!-- 线程池维护线程所允许的空闲时间 -->
        <property name="keepAliveSeconds" value="300"/>
        <!--允许核心线程超时销毁-->
        <property name="allowCoreThreadTimeOut" value="true"/>
        <!-- 线程池对拒绝任务(无线程可用)的处理策略 -->
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>

    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <!--配置中心 -->
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>
    <bean id="autoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:monitor-application.properties"
          p:configName="erp-sync-data-monitor"/>


    <!-- 蜂眼监控 -->
    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"
          p:ignorableSpecialConfigFile="erp-sync-data-monitor"/>
    <aop:config>
        <aop:aspect id="monitor" ref="serviceProfiler">
            <aop:pointcut id="pointCutAround"
                          expression="execution(* com.fxiaoke.open.erpsyncdata.*.service..*.*(..))
                          or execution(* com.fxiaoke.open.erpsyncdata.*.manager.*.*(..))"/>
            <aop:around method="profile" pointcut-ref="pointCutAround"/>
        </aop:aspect>
    </aop:config>

    <bean id="accessLog" class="com.fxiaoke.open.erpsyncdata.dbproxy.interceptor.LogInterceptor">
    </bean>
    <aop:config>
        <aop:aspect id="accessLogMonitor" ref="accessLog" order="0">
            <aop:pointcut id="pointCutAround"
                          expression="execution(* com.fxiaoke.open.erpsyncdata.*.service..*.*(..))
                          or execution(* com.fxiaoke.open.erpsyncdata.*.manager.*.*(..))"/>
            <aop:around method="around" pointcut-ref="pointCutAround"/>
        </aop:aspect>
    </aop:config>


    <!--统一异常捕捉 erp-sync-data-->
    <bean id="erpApiExceptionInterceptor"
          class="com.fxiaoke.open.erpsyncdata.dbproxy.interceptor.ApiExceptionInterceptor"/>
    <aop:config>
        <aop:aspect id="apiExceptionTransfer" ref="erpApiExceptionInterceptor" order="2">
            <aop:around pointcut=" execution(* com.fxiaoke.open.erpsyncdata.*.service..*.*(..)))" method="around"/>
        </aop:aspect>
    </aop:config>

<!--    &lt;!&ndash;访问Pg速度控制 erp-sync-data&ndash;&gt;-->
<!--    <aop:config>-->
<!--        <aop:aspect id="accessPgMonitor" ref="rateLimitHelper" order="20">-->
<!--            <aop:before pointcut="execution(* com.fxiaoke.open.erpsyncdata.dbproxy.dao..*.*(..))"-->
<!--                        method="acquirePermit"/>-->
<!--        </aop:aspect>-->
<!--    </aop:config>-->

    <dubbo:application name="${dubbo.application.name}"/>
    <dubbo:registry address="${dubbo.registry.address}"/>
</beans>