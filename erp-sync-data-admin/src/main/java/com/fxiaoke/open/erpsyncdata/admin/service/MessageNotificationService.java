package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.result.NoticeResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

/**
 * <AUTHOR>
 * @Date: 10:30 2021/2/24
 * @Desc:
 */
public interface MessageNotificationService {

    Result<Void> updateMessageNotificationConfig(String tenantId, Integer userId, String dataCenterId, NoticeResult arg);

    /**
     * 查询通知设置
     * 直接将原接口改造
     * @param tenantId
     * @param dataCenterId
     * @return
     */
    Result<NoticeResult> queryMessageNotificationConfig(String tenantId, String dataCenterId);
}
