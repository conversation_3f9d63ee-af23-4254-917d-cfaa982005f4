package com.fxiaoke.open.erpsyncdata.admin.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.VersionComparator;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.facishare.change.set.module.ConfigPackageInstall;
import com.facishare.change.set.module.appmarket.AppMarketInstall;
import com.facishare.change.set.service.AppMarketService;
import com.facishare.change.set.service.ConfigPackageService;
import com.facishare.converter.EIEAConverter;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.arg.JudgeModuleArg;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.exception.PaasMessage;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.JudgeModulePojo;
import com.facishare.paas.license.pojo.ModuleFlag;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.facishare.uc.api.model.fscore.SimpleEnterprise;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.open.erpsyncdata.admin.arg.CreateConnectorArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetDcBindArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.InitDataCenterInfoArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.QueryDataCenterInfoArg;
import com.fxiaoke.open.erpsyncdata.admin.constant.CreateObjectEnum;
import com.fxiaoke.open.erpsyncdata.admin.manager.CopySettingManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.dataScreen.BIManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.support.CrmObjectSupportManager;
import com.fxiaoke.open.erpsyncdata.admin.model.CopyErpSettingOptions;
import com.fxiaoke.open.erpsyncdata.admin.model.NPathModel;
import com.fxiaoke.open.erpsyncdata.admin.remote.StoneFileManager;
import com.fxiaoke.open.erpsyncdata.admin.service.ConnectInfoService;
import com.fxiaoke.open.erpsyncdata.admin.service.OAConnectorService;
import com.fxiaoke.open.erpsyncdata.admin.service.overseas.LinkedinFieldService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.AplManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.jdy.JDYDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.admin.FunctionInfo;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpConnectService;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RecycleType;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.DataIntegrationNotificationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.RecycleBinDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataIntegrationNotificationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.*;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.AddOAConnectorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.GetConnectorIntroArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.InitErpObjectFieldsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.K3CloudApiConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.Connector;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.ConnectorIntro;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.InnerConnector;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.OuterConnector;
import com.fxiaoke.open.erpsyncdata.preprocess.model.proxyservice.DbProxyServiceInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.DataCenterInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result3;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result4;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AuditLogService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.TenantConfigurationService;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import com.fxiaoke.open.oasyncdata.model.OAConnectInfoVO;
import com.fxiaoke.open.oasyncdata.service.OAConnParamService;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceFindArg;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.fxiaoke.otherrestapi.function.result.FunctionServiceFindResult;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum.DEV_TENANTS;
import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.SYSTEM_ERROR;
import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.UNPURCHASED_PRODUCT;

/**
 * <AUTHOR>
 * @Date: 13:36 2020/8/19
 * @Desc:
 */
@Service
@Slf4j
public class ConnectInfoServiceImpl implements ConnectInfoService {
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ErpConnectService erpConnectService;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpU8EaiConfigDao erpU8EaiConfigDao;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private OAConnParamService oaConnParamService;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private CrmObjectSupportManager crmObjectSupportManager;
    @Autowired
    private ErpObjectAndFieldsServiceImpl erpObjectAndFieldsService;
    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private MigrateTableManager migrateTableManager;
    @Autowired
    private TenantConfigurationService tenantConfigurationService;
    @Resource(name = "redissonClient")
    private RedissonClient redisson;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private StoneFileManager stoneFileManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private JDYDataManager jdyDataManager;
    @Autowired
    private AplManager aplManager;
    @Autowired
    private ErpAlarmRuleManager erpAlarmRuleManager;
    @Autowired
    private BIManager biManager;
    @Autowired
    private OAConnectorService oaConnectorService;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private OuterConnectorManager outerConnectorManager;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private RecycleBinDao recycleBinDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private AdminSyncPloyDetailSnapshotDao adminSyncPloyDetailSnapshotDao;
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;
    @Autowired
    private AuditLogService auditLogService;
    @Autowired
    private ErpHistoryDataTaskDao erpHistoryDataTaskDao;
    @Autowired
    private ErpAlarmRuleDao erpAlarmRuleDao;
    @Autowired
    private DataIntegrationNotificationDao dataIntegrationNotificationDao;

    @Autowired
    private LinkedinFieldService linkedinFieldService;
    @Autowired
    private AppMarketService appMarketService;
    @Autowired
    private CopySettingManager copySettingManager;
    @Autowired
    private DataCenterManager dataCenterManager;
    @Autowired
    private ConfigPackageService configPackageService;

    @Override
    public Result<ConnectInfoResult> getConnectInfoByDataCenterId(String tenantId, int userId, String dataCenterId) {
        return getConnectInfoByDataCenterId(tenantId, dataCenterId);
    }

    public Result<ConnectInfoResult> getConnectInfoByDataCenterId(String tenantId, String dataCenterId) {
        if (StringUtils.isBlank(dataCenterId)) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ErpConnectInfoEntity connectInfo = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, dataCenterId);
        if (connectInfo == null) {
            //原来是new对象返回，改了可能造成一定影响。
            return Result.newError(ResultCodeEnum.CONNECT_INFO_NOT_EXISTS);
        }
        ConnectInfoResult connectInfoResult = entity2ConnectInfo(connectInfo);
        return Result.newSuccess(connectInfoResult);
    }

    @Override
    public Result<ConnectInfoResult> getConnectInfoCheckStatus(String tenantId, int userId, String dataCenterId) {
        Result<ConnectInfoResult> connectInfoResultRes = getConnectInfoByDataCenterId(tenantId, userId, dataCenterId);
        if (!connectInfoResultRes.isSuccess() || connectInfoResultRes.getData() == null) {
            return connectInfoResultRes;
        }
        //授权状态
        ConnectInfoResult data = connectInfoResultRes.getData();
        if (ErpChannelEnum.STANDARD_CHANNEL.equals(data.getChannel()) && data.getConnectParams() != null) {
            StandardConnectParam standard = data.getConnectParams().getStandard();
            if (standard != null && (
                    ConnectorHandlerType.HUB.equals(standard.getConnectorHandlerType())
                            || ConnectorHandlerType.APL_CLASS.equals(standard.getConnectorHandlerType()))) {
                //APL类使用systemParam是否为空判断是否已绑定
                SystemParams systemParams = standard.getSystemParams();
                //当只有authType时，认为未连接
                boolean noBind = systemParams == null
                        || systemParams.isEmpty()
                        || (systemParams.size() == 1 && systemParams.getAuthType() != null);
                data.setBind(!noBind);
                if (!noBind) {
                    String[] msg = new String[1];
                    try {
                        boolean await = ParallelUtils.createParallelTask()
                                .submit(() -> {
                                    Result<Void> checkResult = erpConnectService.checkAuthStatus(tenantId, dataCenterId);
                                    if (!checkResult.isSuccess()) {
                                        msg[0] = checkResult.getErrMsg();
                                    }
                                }).await(3, TimeUnit.SECONDS);
                    } catch (TimeoutException e) {
                        data.setBindExceptionMsg(I18NStringEnum.khqfwztcs.getNameByTraceLocale());
                    }
                    if (msg[0] != null) {
                        data.setBindExceptionMsg(msg[0]);
                    }
                }
            }
        }
        return connectInfoResultRes;
    }


    @Override
    public Result<List<DataCenterInfoResult>> getAllDCInfo(String tenantId, int userId, String lang) {
        //查现有连接信息
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantId(tenantId);
        List<DataCenterInfoResult> results = new ArrayList<>();
        for (ErpConnectInfoEntity erpConnectInfoEntity : erpConnectInfoEntities) {
            if (erpConnectInfoEntity.getChannel().getConnectorType() == ConnectorTypeEnum.OA) continue;
            DataCenterInfoResult dcResult = erpConnectInfoManager.entity2DcInfo(erpConnectInfoEntity);
            results.add(dcResult);
        }
        return Result.newSuccess(results);
    }

    /**
     * 获取数据中心信息，会校验订单
     *
     * @param tenantId
     * @param userId
     * @return
     */
    @Override
    public Result<List<DataCenterInfoResult>> queryDataCenterInfo(String tenantId, int userId, String lang, QueryDataCenterInfoArg arg) {
        //查现有连接信息
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listErpDcByTenantId(tenantId);
        List<DataCenterInfoResult> results = new ArrayList<>();
        for (ErpConnectInfoEntity erpConnectInfoEntity : erpConnectInfoEntities) {
            DataCenterInfoResult dcResult = erpConnectInfoManager.entity2DcInfo(erpConnectInfoEntity);
            dcResult.setHasInited(true);
            //已创建的连接器
            results.add(dcResult);
        }
        // 获取所有连接器,包括删除的
        final List<ErpConnectInfoEntity> entities = erpConnectInfoDao.listErpDcByTenantIdWithDelete(tenantId);
        //默认是渠道name，
        Set<String> existedKeySet = entities.stream()
                .map(entity -> AllConnectorUtil.getByChannelAndConnectParam(entity.getChannel(), entity.getConnectParams()).getKey())
                .collect(Collectors.toSet());
        Set<ErpChannelEnum> purchasedChannel = getValidChannels(tenantId);
        //外部连接器，不再自动新增，必须手动加
        Set<String> purchasedOuterConnectors = getValidOuterConnectors(tenantId);
        //已购买的连接器，内部和外部都在
        Set<String> purchasedConnectorKeys = new HashSet<>(purchasedOuterConnectors);
        purchasedChannel.forEach(v -> purchasedConnectorKeys.add(v.name()));

        Set<String> needInitOutConnectorKeys = new HashSet<>(purchasedConnectorKeys);
        needInitOutConnectorKeys.removeAll(existedKeySet);
        for (String key : needInitOutConnectorKeys) {
            //校验是否存在crm渠道
            getOrCreateCrmDc(tenantId, lang);
            Connector connector = AllConnectorUtil.getByConnectorKey(key);
            //内部连接器,走原来的逻辑
            ErpChannelEnum channel = connector.getChannel();
            DataCenterInfoResult dataCenterInfo = new DataCenterInfoResult();
            dataCenterInfo.setChannel(channel);
            dataCenterInfo.setConnectorKey(key);
            dataCenterInfo.setDataCenterName(connector.getI18nName());
            dataCenterInfo.setConnectorName(connector.getI18nName());
            dataCenterInfo.setIconUrl(connector.getIconUrl());
            //过滤渠道白名单
            if (ErpChannelEnum.UN_START_CONNECT_CHANNEL.contains(channel)) {
                try {
                    if (ErpChannelEnum.OA.equals(channel)) {
                        //调用oa service
                        com.fxiaoke.open.oasyncdata.result.base.Result<List<OAConnectInfoVO>> listResult = oaConnParamService.listInfoByTenantId(tenantId);
                        if (ObjectUtils.isNotEmpty(listResult) && CollectionUtils.isEmpty(listResult.getData())) {
                            dataCenterInfo.setHasInited(false);
                            dataCenterInfo.setHasErpChannel(false);
                            //未初始化的oa
                            results.add(dataCenterInfo);
                            continue;
                        }
                        if (listResult.isSuccess()) {
                            listResult.getData().forEach(item -> {
                                DataCenterInfoResult oaConnectInfo = new DataCenterInfoResult();
                                oaConnectInfo.setChannel(channel);
                                oaConnectInfo.setConnectorName(item.getConnectParams().getConnectOaName());
                                oaConnectInfo.setHasConnect(true);
                                oaConnectInfo.setHasErpChannel(false);
                                oaConnectInfo.setId(item.getId());
                                oaConnectInfo.setHasInited(true);
                                results.add(oaConnectInfo);
                            });
                            continue;
                        }

                    }
                } catch (Exception e) {
                    log.info("oa service fail msg :{}", e.getMessage());
                }
                dataCenterInfo.setHasErpChannel(false);
                dataCenterInfo.setHasInited(true);//因为无需创建，所以默认已经初始化
                //无需创建的连接器
                results.add(dataCenterInfo);
                continue;
            }
            dataCenterInfo.setHasInited(false);
            results.add(dataCenterInfo);
        }
        //是否查询crm连接器
        if (arg.isNeedQueryCrmDataCenter()){
            ErpConnectInfoEntity crmDc = erpConnectInfoManager.getOrCreateCrmDc(tenantId, lang);
            results.add(erpConnectInfoManager.entity2DcInfo(crmDc));
        }
        //未购买的连接器，，，目前的逻辑是，即使已经有连接器，也会显示。
        if (arg.isNeedQueryUnPurchasedDataCenter()){
            List<DataCenterInfoResult> unPurchasedConnectors = getUnPurchasedDcs(purchasedConnectorKeys);
            results.addAll(unPurchasedConnectors);
        }
        //旧云星辰，企微等。
        List<DataCenterInfoResult> notErpConnectorChannelList = getNotErpConnectorChannelList(tenantId,purchasedConnectorKeys, lang);
        if (CollectionUtils.isNotEmpty(notErpConnectorChannelList)) {
            results.addAll(notErpConnectorChannelList);
        }

        if (CollectionUtils.isEmpty(results)) {
            return Result.newError(UNPURCHASED_PRODUCT);
        }
        for (DataCenterInfoResult result : results) {
            result.setConnectorType(result.getChannel().getConnectorType());
            if (result.getConnectorName() == null) {
                result.setConnectorName(i18NStringManager.get(result.getChannel().getI18nKey(), lang, tenantId, result.getChannel().getDefaultName()));
            }
        }
        //初始化钉钉飞书等OA
        Result<List<DataCenterInfoResult>> outerOAConnectorResult = checkAndInitOAConnectorAsync(tenantId, userId, lang);
        if(outerOAConnectorResult.isSuccess()&& CollectionUtils.isNotEmpty(outerOAConnectorResult.getData())){

            //需要移除之前的数据
            List<ErpChannelEnum> outerOAChannelS = outerOAConnectorResult.getData().stream().map(DataCenterInfoResult::getChannel).collect(Collectors.toList());
            results.removeIf(item -> outerOAChannelS.contains(item.getChannel()));
            results.addAll(outerOAConnectorResult.getData());

        }
        Map<String, String> channel2ApplicationMarketId = tenantConfigurationManager.getChannel2ApplicationMarketId();
        for (DataCenterInfoResult result : results) {
            //赋值应用市场id（不覆盖）
            if (result.getChannel() != null && channel2ApplicationMarketId.containsKey(result.getChannel().name()) && StringUtils.isBlank(result.getApplicationMarketId())) {
                result.setApplicationMarketId(channel2ApplicationMarketId.get(result.getChannel().name()));
            }
            //赋值是否已过期
            result.setExpired(!ErpChannelEnum.CRM.equals(result.getChannel()) && !purchasedConnectorKeys.contains(result.getConnectorKey()));
        }
        return Result.newSuccess(results);
    }

    private static @NotNull List<DataCenterInfoResult> getUnPurchasedDcs(Set<String> purchasedConnectorKeys) {
        List<DataCenterInfoResult> unPurchasedConnectors = new ArrayList<>();
        for (Connector connector : AllConnectorUtil.getAllConnectorList()) {
            if (purchasedConnectorKeys.contains(connector.getKey())) {
                //已购买
                continue;
            }
            if (connector instanceof InnerConnector) {
                //内部连接器需要在CHANNEL_CODE_MAP内（原逻辑)
                if (!ErpChannelEnum.CHANNEL_CODE_MAP.containsValue(connector.getChannel())) {
                    continue;
                }
            }
            String applicationMarketId = null;
            if (connector instanceof OuterConnector) {
                //外部连接器，只显示存在产品的（即已上架）
                if (StrUtil.isEmpty(connector.getModuleCode())) {
                    continue;
                }
                applicationMarketId = ((OuterConnector) connector).getApplicationMarketId();
            }
            DataCenterInfoResult dataCenterInfoResult = new DataCenterInfoResult();
            dataCenterInfoResult.setApplicationMarketId(applicationMarketId);
            dataCenterInfoResult.setHasPurchased(false);
            if (ErpChannelEnum.UN_START_CONNECT_CHANNEL.contains(connector.getChannel())) {
                dataCenterInfoResult.setHasErpChannel(false);
            }
            dataCenterInfoResult.setChannel(connector.getChannel());
            dataCenterInfoResult.setConnectorKey(connector.getKey());
            dataCenterInfoResult.setConnectorName(connector.getI18nName());
            dataCenterInfoResult.setIconUrl(connector.getIconUrl());
            //未开通的连接器
            unPurchasedConnectors.add(dataCenterInfoResult);
        }
        return unPurchasedConnectors;
    }

    public Result<List<DataCenterInfoResult>> checkAndInitOAConnectorAsync(String tenantId, int userId, String lang) {
        try {
            return checkAndInitOAConnector(tenantId, userId, lang);
        } catch (Exception e) {
            log.warn("checkout init error:{}",e);
        }
        return Result.newError(ResultCodeEnum.ERROR_MSG);
    }

    @Override
    public Result<List<DataCenterInfoResult>> checkAndInitOAConnector(String tenantId, int userId, String lang) {
        Result<List<DataCenterInfoResult>> outerConnectorResult = oaConnectorService.checkAndInitOAConnector(tenantId);
        return outerConnectorResult;
    }

    @Override
    public Result<List<DataCenterInfoResult>> listAddableDataCenter(String tenantId, ConnectorTypeEnum connectorType, String lang) {
        if (connectorType != null && connectorType == ConnectorTypeEnum.OA) {
            return listAddableDataCenterForOA(tenantId, lang);
        }
        Set<ErpChannelEnum> validChannels = getValidChannels(tenantId);
        List<DataCenterInfoResult> results = new ArrayList<>();
        for (ErpChannelEnum channel : validChannels) {
            if (ErpChannelEnum.AddableChannelSet.contains(channel)) {
                DataCenterInfoResult dataCenterInfo = new DataCenterInfoResult();
                dataCenterInfo.setChannel(channel);
                dataCenterInfo.setConnectorName(channel.getNameByTraceLocale());
                results.add(dataCenterInfo);
            }
        }
        Set<String> validOuterConnectors = getValidOuterConnectors(tenantId);
        for (String validOuterConnector : validOuterConnectors) {
            DataCenterInfoResult dataCenterInfo = new DataCenterInfoResult();
            dataCenterInfo.setConnectorKey(validOuterConnector);
            dataCenterInfo.setChannel(ErpChannelEnum.STANDARD_CHANNEL);
            dataCenterInfo.setConnectorName(AllConnectorUtil.getI18nName(validOuterConnector));
            results.add(dataCenterInfo);
        }
        return new Result<>(results);
    }

    private Result<List<DataCenterInfoResult>> listAddableDataCenterForOA(String tenantId, String lang) {
        Set<ErpChannelEnum> validChannels = getValidChannels(tenantId);
        List<DataCenterInfoResult> results = new ArrayList<>();
        boolean hasQywxChannel = false;
        for (ErpChannelEnum channel : validChannels) {
            if (ErpChannelEnum.AddableOAChannelSet.contains(channel)) {
                DataCenterInfoResult dataCenterInfo = new DataCenterInfoResult();
                dataCenterInfo.setChannel(channel);
                dataCenterInfo.setConnectorName(channel.getNameByTraceLocale());
                //模板连接器
                results.add(dataCenterInfo);
                if (channel == ErpChannelEnum.CONNECTOR_QYWX) {
                    hasQywxChannel = true;
                }
            }
        }
        if (!hasQywxChannel) {
            ErpChannelEnum channel = ErpChannelEnum.CONNECTOR_QYWX;
            DataCenterInfoResult dataCenterInfo = new DataCenterInfoResult();
            dataCenterInfo.setChannel(channel);
            dataCenterInfo.setConnectorName(channel.getNameByTraceLocale());
            //添加企微连接器入口
            results.add(dataCenterInfo);
        }

        return new Result<>(results);
    }

    @Override
    public Result<String> addOAConnector(String tenantId, AddOAConnectorArg arg, String lang) {
        if (arg.getChannel().equals(ErpChannelEnum.OA)) {
            com.fxiaoke.open.oasyncdata.result.base.Result<String> result = oaConnParamService.createOAConnectInfo(tenantId, null, arg.getName(), null, false);
            String dcId = result == null || !result.isSuccess() ? null : result.getData();
            return Result.newSuccess(dcId);
        }
        Result<DataCenterInfoResult> dataCenterInfoResultResult = oaConnectorService.enableAddConnect(tenantId, arg.getChannel().name());
        if (dataCenterInfoResultResult.isSuccess()&&dataCenterInfoResultResult.getData()!=null) {
            return Result.newSuccess(dataCenterInfoResultResult.getData().getId());
        }else{
            return Result.newError(dataCenterInfoResultResult.getErrCode(),dataCenterInfoResultResult.getErrMsg());
        }
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 1, timeUnit = TimeUnit.MINUTES)
    private List<DataCenterInfoResult> getNotErpConnectorChannelList(String tenantId,Set<String> purchasedConnectorKeys, String lang) {
        List<DataCenterInfoResult> dataCenterInfoResultList = new ArrayList<>();

        LicenseContext context = new LicenseContext();
        context.setAppId("CRM");
        context.setTenantId(tenantId);
        context.setUserId("-10000");

        QueryProductArg queryProductArg = new QueryProductArg();
        queryProductArg.setLicenseContext(context);
        try {
            LicenseVersionResult result = licenseClient.queryProductVersion(queryProductArg);
            if (result.getErrCode() == 0) {
                if (CollectionUtils.isNotEmpty(result.getResult())) {
                    Map<String, ErpChannelEnum> productVersionMap = ErpChannelEnum.getExtraProductVersionMap();
                    for (ProductVersionPojo productVersionPojo : result.getResult()) {
                        ErpChannelEnum erpChannelEnum = productVersionMap.get(productVersionPojo.getCurrentVersion());
                        if (erpChannelEnum != null) {
                            if (!productVersionPojo.isTrialFlag()) {
                                //已过期的产品，不展示对应的连接器入口
                                if (System.currentTimeMillis() > productVersionPojo.getExpiredTime()) continue;
                            }

                            DataCenterInfoResult dataCenterInfoResult = new DataCenterInfoResult();
                            dataCenterInfoResult.setChannel(erpChannelEnum);
                            dataCenterInfoResult.setDataCenterName(i18NStringManager.get(erpChannelEnum.getI18nKey(), lang, tenantId, erpChannelEnum.getDefaultName()));
                            dataCenterInfoResult.setHasConnect(true);
                            dataCenterInfoResult.setHasPurchased(true);
                            dataCenterInfoResult.setHasErpChannel(false);
//                            dataCenterInfoResult.setExpired(false);
                            purchasedConnectorKeys.add(erpChannelEnum.name());
                            dataCenterInfoResult.setConnectorName(i18NStringManager.get(erpChannelEnum.getI18nKey(), lang, tenantId, erpChannelEnum.getDefaultName()));
                            dataCenterInfoResultList.add(dataCenterInfoResult);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("queryProductVersion exception", e);
        }
        List<DataCenterInfoResult> dcList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataCenterInfoResultList)) {
            for (DataCenterInfoResult dataCenterInfoResult : dataCenterInfoResultList) {
                List<DataCenterInfoResult> list = dcList.stream()
                        .filter(item -> item.getChannel() == dataCenterInfoResult.getChannel())
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) continue;
                dcList.add(dataCenterInfoResult);
            }
        }
        return dcList;
    }

    @Override
    public Result<DataCenterInfoResult> updateDataCenterInfo(String tenantId, int userId, DataCenterInfoResult dataCenterInfo, String lang) {
        ConnectInfoResult connectInfoResult = new ConnectInfoResult();
        BeanUtils.copyProperties(dataCenterInfo, connectInfoResult);
        connectInfoResult.setEnterpriseName(getEnterpriseName(tenantId));
        Result<ConnectInfoResult> updateResult = updateConnectInfo(tenantId, userId, connectInfoResult, lang, false);
        if (!updateResult.isSuccess()) {
            return Result.copy(updateResult);
        }
        ConnectInfoResult updateData = updateResult.getData();
        BeanUtils.copyProperties(updateData, dataCenterInfo);
        dataCenterInfo.setConnectorType(dataCenterInfo.getChannel().getConnectorType());
        return Result.newSuccess(dataCenterInfo);
    }

    @Override
    public Result<String> initDataCenterInfo(String tenantId, int userId, ConnectInfoResult connectInfo, boolean needInitErpConfig, String lang) {
        //检查创建crm连接器
        getOrCreateCrmDc(tenantId, lang).safeData();
        //这里不做配额检查，新建连接器只是在前端做了限制。
        //补充信息,必传
        String connectorKey = connectInfo.getConnectorKey();
        Connector connector = AllConnectorUtil.getByConnectorKey(connectorKey);
        connectInfo.setChannel(connector.getChannel());
        if (connector instanceof OuterConnector) {
            //外部连接器填充信息,不破坏原来的传参
            if (connectInfo.getConnectParams() == null) {
                connectInfo.setConnectParams(new ConnectInfoResult.ConnectParams());
            }
            StandardConnectParam standardConnectParam = connectInfo.getConnectParams().getStandard();
            if (standardConnectParam == null) {
                standardConnectParam = new StandardConnectParam();
                connectInfo.getConnectParams().setStandard(standardConnectParam);
            }
            standardConnectParam.setSystemName(((OuterConnector) connector).getSystemName());
            standardConnectParam.setApiName(connectorKey);
            standardConnectParam.setConnectorHandlerType(connector.getConnectorHandlerType());
            standardConnectParam.setIconUrl(connector.getIconUrl());
        }
        RLock lock = redisson.getLock("erpSyncData:" + "lockUCI" + tenantId);
        //插入的时候加锁
        if (lock.tryLock()) {
            try {
                String id = insertAndInitConnectInfo(tenantId,userId, connectorKey, connectInfo, needInitErpConfig);
                return Result.newSuccess(id);
            } finally {
                lock.unlock();
            }
        } else {
            throw new ErpSyncDataException(I18NStringEnum.s136, tenantId);
        }
    }


    private String getEnterpriseName(String tenantId) {
        GetSimpleEnterpriseArg getSimpleEnterpriseArg = new GetSimpleEnterpriseArg();
        getSimpleEnterpriseArg.setEnterpriseId(Integer.parseInt(tenantId));
        GetSimpleEnterpriseResult simpleEnterpriseResult = enterpriseEditionService.getSimpleEnterprise(getSimpleEnterpriseArg);
        SimpleEnterprise simpleEnterprise = simpleEnterpriseResult.getSimpleEnterprise();
        if (Objects.isNull(simpleEnterprise) || Objects.isNull(simpleEnterprise.getEnterpriseName())) {
            return null;
        }
        return simpleEnterprise.getEnterpriseName();
    }

    /**
     * @param tenantId
     * @return
     */
    @Cached(cacheType = CacheType.LOCAL, expire = 2, timeUnit = TimeUnit.MINUTES)
    public Set<ErpChannelEnum> getValidChannels(String tenantId) {
        //查询license信息
        JudgeModuleArg judgeModuleArg = new JudgeModuleArg();
        LicenseContext context = new LicenseContext();
        context.setAppId("CRM");
        context.setTenantId(tenantId);
        judgeModuleArg.setContext(context);
        judgeModuleArg.setModuleCodes(Lists.newArrayList(ErpChannelEnum.CHANNEL_CODE_MAP.keySet()));
        try {
            com.facishare.paas.license.common.Result result = licenseClient.judgeModule(judgeModuleArg);
            log.info("judge module license,arg:{}.result:{}", judgeModuleArg, result);
            if (result.getErrCode() == PaasMessage.SUCCESS.getCode()) {
                Set<ErpChannelEnum> collect = ((JudgeModulePojo) result.getResult()).getModuleFlags().stream()
                        .filter(ModuleFlag::isFlag)
                        .map(v -> ErpChannelEnum.CHANNEL_CODE_MAP.get(v.getModuleCode()))
                        .collect(Collectors.toSet());
                return collect;
            }
        } catch (Exception e) {
            log.error("judge module license error", e);
        }
        return new HashSet<>();
    }


    /**
     * @param tenantId
     * @return
     */
    @Cached(cacheType = CacheType.LOCAL, expire = 2, timeUnit = TimeUnit.MINUTES)
    public Set<String> getValidOuterConnectors(String tenantId) {
        Set<String> collect = new HashSet<>();
        //开发hub（名称以dev_开头）的连接器，都可以添加
        outerConnectorManager.getHubInfoList().stream()
                .filter(v->StrUtil.startWith(v.getName(),"dev_"))
                .flatMap(v -> Opt.ofTry(() -> v.getOuterConnectors().stream().filter(o->o.getModuleCode()==null)).orElse(Stream.empty()))
                .forEach(v -> {
                    if (v.getApiName() != null) {
                        collect.add(v.getApiName());
                    }
                });
        //查询license信息
        ImmutableMap<String, OuterConnector> codeMapCopy = AllConnectorUtil.getOuterCodeMapCopy();
        JudgeModuleArg judgeModuleArg = new JudgeModuleArg();
        LicenseContext context = new LicenseContext();
        context.setAppId("CRM");
        context.setTenantId(tenantId);
        judgeModuleArg.setContext(context);
        judgeModuleArg.setModuleCodes(Lists.newArrayList(codeMapCopy.keySet()));
        try {
            com.facishare.paas.license.common.Result result = licenseClient.judgeModule(judgeModuleArg);
            log.info("judge module license,arg:{}.result:{}", judgeModuleArg, result);
            if (result.getErrCode() == PaasMessage.SUCCESS.getCode()) {
                for (ModuleFlag moduleFlag : ((JudgeModulePojo) result.getResult()).getModuleFlags()) {
                    if (moduleFlag.isFlag()) {
                        OuterConnector outerConnector = codeMapCopy.get(moduleFlag.getModuleCode());
                        if (outerConnector != null) {
                            collect.add(outerConnector.getApiName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("judge module license error", e);
        }
        return collect;
    }

    @Override
    public Result<List<ConnectInfoResult>> queryConnectInfo(String tenantId, int userId) {
        try {
            ErpConnectInfoEntity arg = new ErpConnectInfoEntity();
            arg.setTenantId(tenantId);
            List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(arg);
            if (CollectionUtils.isEmpty(erpConnectInfoEntities)) {
                return Result.newSuccess(Lists.newArrayList(new ConnectInfoResult()));
            }
            List<ConnectInfoResult> connectInfos = Lists.newArrayList();
            for (ErpConnectInfoEntity erpConnectInfoEntity : erpConnectInfoEntities) {
                ConnectInfoResult connectInfoResult = entity2ConnectInfo(erpConnectInfoEntity);
                connectInfos.add(connectInfoResult);
            }
            return Result.newSuccess(connectInfos);
        } catch (Exception e) {
            Result result = Result.newError(SYSTEM_ERROR);
            result.setData(e.getMessage());
            return result;
        }
    }

    @NotNull
    private static ConnectInfoResult entity2ConnectInfo(ErpConnectInfoEntity erpConnectInfoEntity) {
        ConnectInfoResult connectInfoResult = new ConnectInfoResult();
        connectInfoResult.setBind(connectInfoResult.getConnectParams() != null);
        BeanUtils.copyProperties(erpConnectInfoEntity, connectInfoResult);
        connectInfoResult.setNumber(erpConnectInfoEntity.getNumber());
        String connectStr = erpConnectInfoEntity.getConnectParams();
        ErpChannelEnum channel = erpConnectInfoEntity.getChannel();
        if (StringUtils.isNotEmpty(connectStr)) {
            final BaseConnectParam connectParam = channel.getConnectParam(connectStr);
            connectInfoResult.setConnectorName(connectParam.getConnectorName());
            connectInfoResult.setConnectParams(channel.getNewConnectParam(connectParam));
            Connector connector = AllConnectorUtil.getByChannelAndConnectParam(channel, connectStr);
            connectInfoResult.setConnectorKey(connector.getKey());
            List<String> pushDataApiNames = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(connectParam.getPushDataApiNames())) {
                pushDataApiNames.addAll(connectParam.getPushDataApiNames());
            }
            connectInfoResult.setPushDataApiNames(pushDataApiNames);
        }
        connectInfoResult.setConnectorNameIfAbsent(channel.getNameByTraceLocale());
        return connectInfoResult;
    }

    @Override
    public Result<ConnectInfoResult> updateConnectInfo(String tenantId, int userId, ConnectInfoResult connectInfo, String lang, boolean needUpdateAfterInsert) {
        //校验和加工
        //校验centerName是否已存在
        ErpConnectInfoEntity isExistInfo = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryInfoByName(tenantId, connectInfo.getDataCenterName());
        if (ObjectUtils.isNotEmpty(isExistInfo) && !isExistInfo.getId().equals(connectInfo.getId())) {
            return Result.newError(ResultCodeEnum.DUPLICATE_CENTER_NAME);
        }
        String dcId = connectInfo.getId();
        boolean insert = StringUtils.isEmpty(dcId);
        //加工信息。带有校验
        ErpConnectInfoEntity erpConnectInfoEntity = convertResult2Entity(tenantId, userId, connectInfo,insert);
        if (insert) {
            //初始化，将原来ctrl c ctrl v的代码统合了。
            Result<String> createResult = initDataCenterInfo(tenantId, userId, connectInfo, true, lang);
            if (!createResult.isSuccess()) {
                return Result.copy(createResult);
            }
            dcId = createResult.getData();
            //新建后设置dcId
            erpConnectInfoEntity.setId(dcId);
            connectInfo.setId(dcId);
        }
        if (!insert || needUpdateAfterInsert) {
            try{
                //标准连接器检查连接参数，，，只在更新的时候执行。inner新增的，需要等新增完成（可能是应用安装），之后再执行
                checkSystemParamByConnector(tenantId,dcId, connectInfo, erpConnectInfoEntity);
                //更新 或插入后更新
                erpConnectInfoEntity.setUpdateTime(System.currentTimeMillis());
                int updateResult = erpConnectInfoManager.updateById(tenantId, erpConnectInfoEntity);
                if (updateResult != 1) {
                    return Result.newError(SYSTEM_ERROR);
                }
            } catch (Exception e) {
                if (insert) {
                    //插入之后的更新异常时，需要执行删除连接器(物理删除连接器）。
                    final int i = erpConnectInfoDao.deleteByTenantIdAndId(tenantId, erpConnectInfoEntity.getId());
                }
                throw e;
            }
            if (connectInfo.getChannel() == ErpChannelEnum.ERP_LINKEDIN) {
                // 如果是海外连接器,初始化对象
                ParallelUtils.createParallelTask().submit(() -> {
                    try {
                        linkedinFieldService.initObject(tenantId, connectInfo.getId());
                    } catch (Exception e) {
                        log.error("init overseas field error", e);
                    }
                }).run();
            }
        }
        erpAlarmRuleManager.checkAndInitAlarmRuleData(tenantId, dcId, lang);
        return getConnectInfoCheckStatus(tenantId, userId, dcId);
    }

    @Override
    public Result<ConnectInfoResult> upsertConnectInfoAndCopySetting(String tenantId, int userId, ConnectInfoResult connectInfo, String lang, boolean needUpdateAfterInsert, boolean needCopySetting) {
        boolean isInsert = StrUtil.isBlank(connectInfo.getId());
        Result<ConnectInfoResult> connectInfoResultResult = this.updateConnectInfo(tenantId, userId, connectInfo, lang, needUpdateAfterInsert);
        if (isInsert
                && connectInfoResultResult.isSuccess()
                && needCopySetting) {
            int newSeq = dataCenterManager.getDataCenterSeq(tenantId, connectInfoResultResult.getData().getId());
            if (newSeq > 0) {
                //不是第一个连接器，从第一个连接器获取
                Connector connector = AllConnectorUtil.getByConnectorKey(connectInfo.getConnectorKey());
                Integer connectorId = connector.getConnectorId();
                ErpConnectInfoEntity firstConnectInfo = erpConnectInfoDao.getByNumber(tenantId, connectInfo.getChannel(), connectorId * 100);
                if (firstConnectInfo != null) {
                    copySettingManager.copyErpSetting(tenantId, firstConnectInfo.getId(), connectInfoResultResult.getData().getId(), CopyErpSettingOptions.of().oldSuffix("_0").newSuffix("_" + newSeq).copyAllStream(true));
                }
            }
        }
        return connectInfoResultResult;
    }

    private ErpConnectInfoEntity convertResult2Entity(String tenantId, int userId, ConnectInfoResult connectInfo,boolean isInsert) {
        ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
        BeanUtils.copyProperties(connectInfo, erpConnectInfoEntity);
        // 这里做了连接信息的校验
        erpConnectInfoEntity.setConnectParams(checkConnectParam2Json(connectInfo, tenantId));
        erpConnectInfoEntity.setTenantId(tenantId);
        if (connectInfo.getConnectParams() == null) {
            //没有连接信息的不做后处理
            return erpConnectInfoEntity;
        }
        //新增时，如果带有参数，也执行
        if (Objects.equals(connectInfo.getChannel(), ErpChannelEnum.STANDARD_CHANNEL)) {
            StandardConnectParam connectParam = connectInfo.getConnectParams().getStandard();
            if (connectParam == null) {
                return erpConnectInfoEntity;
            }
            Connector connector;
            if (StrUtil.isNotBlank(connectInfo.getConnectorKey())) {
                //优先从connectorKey获取连接器
                connector = AllConnectorUtil.getByConnectorKey(connectInfo.getConnectorKey());
                //设置handlerType
                connectParam.setConnectorHandlerType(connector.getConnectorHandlerType());
            } else {
                //从连接信息的handlerType获取连接信息
                connector = AllConnectorUtil.getStandardConnector(connectParam);
            }
            // 保存标准连接器图标
            saveStandardConnectTnPathIcon(tenantId, userId, connectParam);

            //检查连接器，可能新增外部连接器
            checkConnectorExistAndCreate(tenantId, erpConnectInfoEntity, connectParam);
            if (connector instanceof OuterConnector) {
                //外部连接器,替换系统名称和icon
                OuterConnector outerConnector = (OuterConnector) connector;
                if (connectParam.getSystemName() == null) {
                    connectParam.setSystemName(outerConnector.getSystemName());
                }
                if (connectParam.getIconUrl() == null) {
                    connectParam.setIconUrl(outerConnector.getIconUrl());
                }
            }
            erpConnectInfoEntity.setConnectParams(checkConnectParam2Json(connectInfo, tenantId));
        }
        return erpConnectInfoEntity;
    }

    /**
     * 连接器实现的检查
     */
    private void checkSystemParamByConnector(String tenantId,String dcId, ConnectInfoResult connectInfo, ErpConnectInfoEntity erpConnectInfoEntity) {
        if (!Objects.equals(connectInfo.getChannel(), ErpChannelEnum.STANDARD_CHANNEL)) {
            //仅处理自定义连接器
            return;
        }
        StandardConnectParam connectParam = connectInfo.getConnectParams().getStandard();
        if (connectParam == null) {
            return;
        }
        SystemParams systemParams = connectParam.getSystemParams();
        if (systemParams != null && !systemParams.isEmpty()) {
            ConnectorAuthType authType = connectParam.getAuthType();
            GetConnectorIntroArg arg = new GetConnectorIntroArg();
            arg.setAuthType(authType);
            ConnectorIntro connectorIntro = erpConnectService.getConnectorIntro(tenantId, dcId, arg).safeData();
            //填充缺失的默认值，包括了authUri和webhookUri
            if (connectorIntro != null && connectorIntro.getParamSchemas() != null) {
                for (ConnectorIntro.ParamSchema paramSchema : connectorIntro.getParamSchemas()) {
                    if (paramSchema.getDefaultValue() != null) {
                        systemParams.putIfAbsent(paramSchema.getKey(), paramSchema.getDefaultValue());
                    }
                }
            }
            //连接信息不为空时才处理
            //固定增加AuthType
            systemParams.setAuthType(authType);
            //当连接参数不为空，处理输入参数。即允许更新为null
            systemParams = erpConnectService.processUserInputSystemParams(tenantId, dcId, systemParams).safeData();
            connectParam.setSystemParams(systemParams);
            erpConnectInfoEntity.setConnectParams(checkConnectParam2Json(connectInfo, tenantId));
        }
    }

    private void saveStandardConnectTnPathIcon(String tenantId, int userId, StandardConnectParam connectParam) {
        if (StringUtils.isBlank(connectParam.getIconUrl()) || !connectParam.getIconUrl().startsWith("TN_")) {
            return;
        }

        connectParam.setIconUrl(stoneFileManager.saveImageFromTempImagePathByTenantId(tenantId, userId, connectParam.getIconUrl()));
    }

    private void checkConnectorExistAndCreate(String tenantId, ErpConnectInfoEntity connectInfo, StandardConnectParam connectParam) {
        ErpConnectInfoEntity oldEntity = null;
        if (StrUtil.isNotBlank(connectInfo.getId())) {
            oldEntity = erpConnectInfoDao.getByIdAndTenantId(tenantId, connectInfo.getId());
        }
        //旧连接器类型
        ConnectorHandlerType oldType = ConnectorHandlerType.REST_API;
        if (oldEntity != null && oldEntity.getConnectParams() != null) {
            StandardConnectParam oldConnectParam = ErpChannelEnum.STANDARD_CHANNEL.getConnectParam(oldEntity.getConnectParams());
            if (oldConnectParam != null) {
                oldType = oldConnectParam.getConnectorHandlerType();
            }
        }

        ConnectorHandlerType newType = connectParam.getConnectorHandlerType();
        if (oldType == ConnectorHandlerType.APL_CLASS && newType != ConnectorHandlerType.APL_CLASS) {
            //APL连接器不允许修改 handlerType
            throw new ErpSyncDataException("APL CLASS Connector cannot change type ,please create a new connector");
        }
        if (newType == ConnectorHandlerType.APL_CLASS) {
            if (oldEntity != null && oldType != ConnectorHandlerType.APL_CLASS) {
                //校验是否有权限更换连接类型,新建时，不校验
                Boolean isDev = tenantConfigurationManager.inWhiteList(tenantId, DEV_TENANTS);
                if (!isDev) {
                    throw new ErpSyncDataException("Only developers can change connectorHandlerType");
                }
            }
            if (StrUtil.isEmpty(connectParam.getApiName())) {
                throw new ErpSyncDataException(I18NStringEnum.kbkwk.indexedFormat("apiName"));
            }
            //任何的APL连接器，都增加到外部连接器列表
            OuterConnector outerConnector = AllConnectorUtil.getOuterByApiName(connectParam.getApiName());
            if (outerConnector == null) {
                //增加Connector
                outerConnector = OuterConnector.builder()
                        .apiName(connectParam.getApiName())
                        .connectorHandlerType(ConnectorHandlerType.APL_CLASS)
                        .systemName(connectParam.getSystemName())
                        .defaultName(connectInfo.getDataCenterName())
                        .build();
                outerConnectorManager.addAPLClassConnector(outerConnector);
                outerConnector = AllConnectorUtil.getOuterByApiName(connectParam.getApiName());
                //更换连接器编码
                Integer connectorNum = outerConnector.getConnectorId() * 100;
                connectInfo.setNumber(connectorNum);
                erpConnectInfoDao.updateNumber(connectorNum, tenantId, connectInfo.getId());
            }
        }
    }

    //needInitErpConfig 为false 则只新增数据中心，不初始化其他配置。
    private String insertAndInitConnectInfo(String tenantId,
                                            int userId,
                                            String connectorKey,
                                            ConnectInfoResult connectInfo,
                                            boolean needInitErpConfig) {
        //校验centerName是否已存在
        ErpConnectInfoEntity isExistInfo = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryInfoByName(tenantId, connectInfo.getDataCenterName());
        if (ObjectUtils.isNotEmpty(isExistInfo)) {
            throw new ErpSyncDataException(ResultCodeEnum.DUPLICATE_CENTER_NAME);
        }

        //创建专表
        try {
            migrateTableManager.initTenantTable(tenantId);
        } catch (SQLException e) {
            log.info("init table occurs a SQLException", e);
            throw new ErpSyncDataException(I18NStringEnum.s137, tenantId);
        }
        //获取编码
        Connector connector = AllConnectorUtil.getByConnectorKey(connectorKey);
        Integer connectorId = connector.getConnectorId();
        int minNum = connectorId * 100;
        int nextNum = erpConnectInfoManager.findNextNum(tenantId, connectorId);
        boolean inserted = false;
        String id = com.fxiaoke.api.IdGenerator.get();
        if (connector instanceof OuterConnector) {
            if (nextNum == minNum) {
                //首个连接器， 安装应用
                OuterConnector outerConnector = (OuterConnector) connector;
                String applicationMarketId = outerConnector.getApplicationMarketId();
                String installId = outerConnector.getInstallId();
                if (outerConnector.isNeedInstall() && !StrUtil.isAllNotBlank(installId, applicationMarketId)) {
                    if (StrUtil.isNotBlank(installId)) {
                        ConfigPackageInstall.Arg arg = new ConfigPackageInstall.Arg();
                        arg.setEnterpriseId(Integer.valueOf(tenantId));
                        arg.setUserId(-10000);
                        arg.setInstallId(installId);
                        ConfigPackageInstall.Result result = configPackageService.InstallConfigPackage(arg);
                        log.info("begin configPackageService.InstallConfigPackage {},{}", outerConnector.getApiName(), installId);
                        if (!result.getSuccess()) {
                            throw ErpSyncDataException.wrap(I18NStringEnum.kpzbazsb);
                        }
                    } else if (StrUtil.isNotBlank(applicationMarketId)) {
                        AppMarketInstall.Request request = new AppMarketInstall.Request();
                        request.setEnterpriseId(Integer.valueOf(tenantId));
                        request.setEmployeeId(userId);
                        request.setSoftwareAppId(applicationMarketId);
                        AppMarketInstall.Result result = appMarketService.install(request);
                        log.info("begin appMarketService.install {},{}", outerConnector.getApiName(), applicationMarketId);
                        if (result.getCode() != 0) {
                            throw new ErpSyncDataException(I18NStringEnum.kpzbazsb.getText() + ":" + result.getMessage());
                        }
                    }
                    //等待时间列表
                    ArrayList<Long> delays = CollUtil.newArrayList(500L);
                    for (int i = 0; i < 60; i++) {
                        //等待60s
                        delays.add(1000L);
                    }
                    //等待更改集插入数据
                    ErpConnectInfoEntity newEntity = WaitUtil.fetchResourceWithDelays(() -> {
                        ErpConnectInfoEntity exist = erpConnectInfoDao.getByNumber(tenantId, ErpChannelEnum.STANDARD_CHANNEL, nextNum);
                        return exist;
                    }, delays);
                    if (newEntity == null) {
                        throw ErpSyncDataException.wrap(I18NStringEnum.kpzbazsb);
                    }
                    id = newEntity.getId();
                    inserted = true;
                }
            }
        }
        if (!inserted) {
            ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
            BeanUtils.copyProperties(connectInfo, erpConnectInfoEntity);
            erpConnectInfoEntity.setTenantId(tenantId);
            erpConnectInfoEntity.setEnterpriseName(getEnterpriseName(tenantId));
            erpConnectInfoEntity.setId(id);
            erpConnectInfoEntity.setConnectParams(checkConnectParam2Json(connectInfo, tenantId));
            erpConnectInfoEntity.setCreateTime(System.currentTimeMillis());
            erpConnectInfoEntity.setUpdateTime(System.currentTimeMillis());
            erpConnectInfoEntity.setNumber(nextNum);
            log.info("ConnectInfoServiceImpl.insertAndInitConnectInfo,erpConnectInfoEntity={}", erpConnectInfoEntity);
            int insertResult = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(erpConnectInfoEntity);
            inserted = insertResult == 1;
        }
        if (inserted) {
            if (needInitErpConfig) {
                this.initErpConfig(tenantId, id, connectInfo.getChannel());
            }
        } else {
            throw new ErpSyncDataException(I18NStringEnum.s138, tenantId);
        }
        return id;
    }

    public void initErpConfig(String tenantId, String dataCenterId, ErpChannelEnum channel) {
        switch (channel) {
            case ERP_U8:
                initU8Config(tenantId, dataCenterId, channel);
                return;
            case ERP_U8_EAI:
                Result<ErpTenantConfiguration> erpTenantConfiguration =
                        tenantConfigurationService.queryConfig("0", "0", channel.name(), "U8_EAI_DEFAULT_TENANTID");
                if (erpTenantConfiguration.getData() == null || !erpTenantConfiguration.isSuccess() || StringUtils.isEmpty(erpTenantConfiguration.getData().getConfiguration())) {
                    return;
                }
                initU8EaiConfig(erpTenantConfiguration.getData().getConfiguration(), tenantId, dataCenterId, channel);
                return;
            case ERP_K3CLOUD:
                initK3CloudConfig(tenantId, dataCenterId, channel);
                return;
            case ERP_K3CLOUD_ULTIMATE:
                initK3UltimateConfig(tenantId, dataCenterId, channel);
                return;
            case ERP_JDY:
                initjdyCloudConfig(tenantId, dataCenterId, channel);
                return;
            case ERP_LINKEDIN:
                linkedinFieldService.initObject(tenantId, dataCenterId);
                return;
            default:
        }
    }

    public void initU8EaiConfig(String defaultTenantId, String tenantId, String dataCenterId, ErpChannelEnum channel) {
        //初始化EAI配置
        ErpU8EaiConfigEntity erpU8EaiConfigEntity = new ErpU8EaiConfigEntity();
        erpU8EaiConfigEntity.setTenantId(defaultTenantId);
        ErpConnectInfoEntity connectInfoEntity = new ErpConnectInfoEntity();
        connectInfoEntity.setTenantId(defaultTenantId);
        connectInfoEntity.setChannel(channel);
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(defaultTenantId)).queryList(connectInfoEntity);
        String defaultDataCenterId = "0";
        if (CollectionUtils.isNotEmpty(erpConnectInfoEntities)) {
            List<ErpConnectInfoEntity> connectInfoEntities = erpConnectInfoEntities.stream()
                    .filter(t -> StringUtils.isNotEmpty(t.getConnectParams()))
                    .collect(Collectors.toList());
            defaultDataCenterId = connectInfoEntities.get(0).getId();
        }

        copyObjAndObjRelationAndField(defaultTenantId, defaultDataCenterId, tenantId, dataCenterId, channel);

        ErpU8EaiConfigEntity entity = new ErpU8EaiConfigEntity();
        entity.setTenantId(defaultTenantId);
        entity.setDataCenterId(defaultDataCenterId);
        List<ErpU8EaiConfigEntity> erpU8EaiConfigEntities = erpU8EaiConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(defaultTenantId)).queryList(entity);

        List<ErpU8EaiConfigEntity> newErpU8EaiConfigEntitys = BeanUtil.deepCopyList(erpU8EaiConfigEntities, ErpU8EaiConfigEntity.class);
        for (ErpU8EaiConfigEntity configEntity : newErpU8EaiConfigEntitys) {
            configEntity.setId(idGenerator.get());
            configEntity.setTenantId(tenantId);
            configEntity.setDataCenterId(dataCenterId);
            configEntity.setCreateTime(System.currentTimeMillis());
            configEntity.setUpdateTime(System.currentTimeMillis());
            configEntity.setLastModifyBy("1000");
        }
        if (CollectionUtils.isNotEmpty(newErpU8EaiConfigEntitys)) {
            erpU8EaiConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(newErpU8EaiConfigEntitys);
        }
    }

    private void initU8Config(String tenantId, String dataCenterId, ErpChannelEnum channel) {
        copyObjAndObjRelationAndField("0", null, tenantId, dataCenterId, channel);
    }

    public void copyObjAndObjRelationAndField(String defaultTenantId, String defaultDataCenterId, String tenantId, String dataCenterId, ErpChannelEnum channel) {
        ErpConnectInfoEntity connQuery = new ErpConnectInfoEntity();
        connQuery.setTenantId(tenantId);
        connQuery.setChannel(channel);
        String suffixName = "";
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(connQuery);//已存在该渠道的连接信息个数
        if (erpConnectInfoEntities.size() == 1) {
            suffixName = "";
        } else {
            suffixName += erpConnectInfoEntities.size() - 1;
        }
        //复制渠道对应的默认配置对象
        ErpObjectEntity erpObjectEntity = new ErpObjectEntity();
        erpObjectEntity.setTenantId(defaultTenantId);
        erpObjectEntity.setChannel(channel);
        erpObjectEntity.setDataCenterId(defaultDataCenterId);
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(defaultTenantId)).queryList(erpObjectEntity);
        List<ErpObjectEntity> newErpObjectEntities = BeanUtil.deepCopyList(erpObjectEntities, ErpObjectEntity.class);
        for (ErpObjectEntity objectEntity : newErpObjectEntities) {
            objectEntity.setTenantId(tenantId);
            objectEntity.setDataCenterId(dataCenterId);
            objectEntity.setId(idGenerator.get());
            objectEntity.setCreateTime(System.currentTimeMillis());
            objectEntity.setUpdateTime(System.currentTimeMillis());
            if (!ErpObjectTypeEnum.REAL_OBJECT.equals(objectEntity.getErpObjectType())) {
                //复制拆分对象,同时复制渠道对应的默认配置对象对应关系
                ErpObjectRelationshipEntity erpObjectRelationshipEntity = new ErpObjectRelationshipEntity();
                erpObjectRelationshipEntity.setTenantId(defaultTenantId);
                erpObjectRelationshipEntity.setChannel(channel);
                erpObjectRelationshipEntity.setErpSplitObjectApiname(objectEntity.getErpObjectApiName());
                erpObjectRelationshipEntity.setDataCenterId(defaultDataCenterId);
                ErpObjectRelationshipEntity objectRelationshipEntity =
                        erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(defaultTenantId)).queryList(erpObjectRelationshipEntity).get(0);

                objectRelationshipEntity.setTenantId(tenantId);
                objectRelationshipEntity.setDataCenterId(dataCenterId);
                objectRelationshipEntity.setChannel(channel);
                objectRelationshipEntity.setId(idGenerator.get());
                objectRelationshipEntity.setCreateTime(System.currentTimeMillis());
                objectRelationshipEntity.setUpdateTime(System.currentTimeMillis());

                String erpObjectApiName = objectEntity.getErpObjectApiName();
                String newErpObjApiName = erpObjectApiName + suffixName;
                objectEntity.setErpObjectApiName(newErpObjApiName);
                objectRelationshipEntity.setErpSplitObjectApiname(newErpObjApiName);
                erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(objectRelationshipEntity);

                //复制拆分对象的字段映射
                ErpObjectFieldEntity erpObjectFieldEntity = new ErpObjectFieldEntity();
                erpObjectFieldEntity.setTenantId(defaultTenantId);
                erpObjectFieldEntity.setChannel(channel);
                erpObjectFieldEntity.setDataCenterId(defaultDataCenterId);
                erpObjectFieldEntity.setErpObjectApiName(erpObjectApiName);

                List<ErpObjectFieldEntity> erpObjectFieldEntities = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(defaultTenantId)).queryList(erpObjectFieldEntity);
                List<ErpObjectFieldEntity> newErpObjectFieldEntities = BeanUtil.deepCopyList(erpObjectFieldEntities, ErpObjectFieldEntity.class);

                for (ErpObjectFieldEntity objectFieldEntity : newErpObjectFieldEntities) {
                    objectFieldEntity.setTenantId(tenantId);
                    objectFieldEntity.setDataCenterId(dataCenterId);
                    objectFieldEntity.setId(idGenerator.get());
                    objectFieldEntity.setCreateTime(System.currentTimeMillis());
                    if (ErpFieldTypeEnum.object_reference.equals(objectFieldEntity.getFieldDefineType())
                            || ErpFieldTypeEnum.master_detail.equals(objectFieldEntity.getFieldDefineType())) {
                        objectFieldEntity.setFieldExtendValue(objectFieldEntity.getFieldExtendValue() + suffixName);
                    }
                    objectFieldEntity.setErpObjectApiName(newErpObjApiName);
                    //erpObjectFieldDao.insert(objectFieldEntity);
                }
                if (CollectionUtils.isNotEmpty(newErpObjectFieldEntities)) {
                    erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(newErpObjectFieldEntities);
                }
            }
            erpObjectRelationshipDao.invalidCacheErpObj(tenantId, dataCenterId);
            //erpObjectDao.insert(objectEntity);
        }
        erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(newErpObjectEntities);
    }

    /**
     * 初始化K3字段配置，对象名不增加ei了。
     * 此处可优化为批量插入！
     *
     * @param tenantId
     * @param channel
     * @return
     */
    private void initK3CloudConfig(String tenantId, String dataCenterId, ErpChannelEnum channel) {
        boolean createObj = crmObjectSupportManager.createDefineObject(
                Integer.valueOf(tenantId), ObjectApiNameEnum.FS_ERP_ORGANIZATION_OBJ.getObjApiName());
        if (!createObj) {
            log.error("create obj may be failed.");
        }
        InitErpObjectFieldsArg initErpObjectFieldsArg = new InitErpObjectFieldsArg();
        initErpObjectFieldsArg.setChannel(channel);
        initErpObjectFieldsArg.setTargetTenantId(tenantId);
        initErpObjectFieldsArg.setDataCenterId(dataCenterId);
        Result<Void> result = erpObjectAndFieldsService.initObjAndFields(initErpObjectFieldsArg);
        if (!result.isSuccess()) {
            int i = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByEiAndId(tenantId, dataCenterId);
            log.error("init obj and field failed ,delete connect info,ei:{},dcId:{},res:{}", tenantId, dataCenterId, i);
            throw new ErpSyncDataException(result.getErrCode(), result.getErrMsg(), result.getI18nKey(), tenantId);
        }
    }

    /**
     * 初始化K3Ultimate默认配置
     *
     * @param tenantId
     * @param dataCenterId
     * @param channel
     */
    private void initK3UltimateConfig(String tenantId, String dataCenterId, ErpChannelEnum channel) {
        boolean createObj = crmObjectSupportManager.createDefineObject(
                Integer.valueOf(tenantId), ObjectApiNameEnum.FS_ERP_ORGANIZATION_OBJ.getObjApiName());
        if (!createObj) {
            log.error("create obj may be failed.");
        }
    }

    /**
     * 初始化jdy字段配置，
     *
     * @param tenantId
     * @param channel
     * @return
     */
    private void initjdyCloudConfig(String tenantId, String dataCenterId, ErpChannelEnum channel) {

        InitErpObjectFieldsArg initErpObjectFieldsArg = new InitErpObjectFieldsArg();
        initErpObjectFieldsArg.setChannel(channel);
        initErpObjectFieldsArg.setTargetTenantId(tenantId);
        initErpObjectFieldsArg.setDataCenterId(dataCenterId);
        Result<Void> result = erpObjectAndFieldsService.initObjAndFields(initErpObjectFieldsArg);
        if (!result.isSuccess()) {
            int i = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByEiAndId(tenantId, dataCenterId);
            log.error("init obj and field failed ,delete connect info,ei:{},dcId:{},res:{}", tenantId, dataCenterId, i);
            throw new ErpSyncDataException(result.getErrCode(), result.getErrMsg(), result.getI18nKey(), tenantId);
        }
    }

    private static boolean canGetServiceInfo(DBProxyConnectParam dbProxyParam) {
        String version = dbProxyParam.getVersion();
        boolean needCheckService = VersionComparator.INSTANCE.compare(version, "1.1") >= 0;
        return needCheckService;
    }

    private String checkConnectParam2Json(ConnectInfoResult connectInfo, String tenantId) {
        //这是写到了外面的特殊逻辑，抽到这里
        if (connectInfo.getConnectParams() == null) {
            if (connectInfo.getChannel() == ErpChannelEnum.YXT_KUAISHOU_XIANSU
                    || connectInfo.getChannel() == ErpChannelEnum.YXT_SOGO_XIANSUO
                    || connectInfo.getChannel() == ErpChannelEnum.YXT_UC_SHENMA) {
                connectInfo.setConnectParams(new ConnectInfoResult.ConnectParams());
                connectInfo.getConnectParams().setYxtCommon(new YxtCommonConnectParam());
            } else if (connectInfo.getChannel() == ErpChannelEnum.CONNECTOR_QYWX) {
                connectInfo.setConnectParams(new ConnectInfoResult.ConnectParams());
                connectInfo.getConnectParams().setQywx(new QYWXConnectParam());
            } else if (connectInfo.getChannel() == ErpChannelEnum.CONNECTOR_FEISHU) {
                connectInfo.setConnectParams(new ConnectInfoResult.ConnectParams());
                connectInfo.getConnectParams().setFeiShu(new FeiShuConnectParam());
            } else {
                //不改变原来逻辑，返回null
                return null;
            }
        }
        final BaseConnectParam connectParam1 = connectInfo.getChannel().getConnectParam(connectInfo.getConnectParams());
        connectParam1.setPushDataApiNames(connectInfo.getPushDataApiNames());
        connectParam1.setBaseUrl(StringUtils.trimToEmpty(connectParam1.getBaseUrl()));
        switch (connectInfo.getChannel()) {
            case ERP_SAP:
                SapConnectParam param = (SapConnectParam) connectParam1;
                if (param.getServicePath() == null) {
                    param.setServicePath(new SapConnectParam.ServicePath());
                }
                if(SAPConnectEnum.SAP_RFC.name().equals(param.getConnectType())){
                    //设置baseurl
                    String sapProxyUrl = param.getSaprfcParams().getSapProxyUrl();
                    param.setBaseUrl(sapProxyUrl);
                }

                break;
            case ERP_K3CLOUD:
                K3CloudConnectParam connectParam = (K3CloudConnectParam) connectParam1;
                if (connectParam.configIsNull() && !StringUtils.isEmpty(connectInfo.getId())) {
                    //如果未传输，则取原来配置
                    ErpConnectInfoEntity oldEntity = erpConnectInfoDao.getByIdAndTenantId(tenantId, connectInfo.getId());
                    if (oldEntity != null && oldEntity.getConnectParams() != null) {
                        K3CloudConnectParam oldParam = GsonUtil.fromJson(oldEntity.getConnectParams(), K3CloudConnectParam.class);
                        connectParam.setConfig(oldParam.newConfigIfNull());
                    } else {
                        connectParam.setConfig(new K3CloudApiConfig());
                    }
                }
                if ("https://api.kingdee.com/galaxyapi/".equals(StringUtils.appendIfMissing(connectParam.getBaseUrl(), "/"))) {
                    //使用k3网关地址时，统一改用appToken的方式访问接口。8.1后公有云只能通过网关访问。
                    connectParam.newConfigIfNull().setUseAppToken(true);
                }
                //修改或者编辑的时候，保存http为true
                connectParam.setUseFsHttpClient(true);
                connectParam.setPushDataApiNames(connectInfo.getPushDataApiNames());
                Result<Void> checkResult = erpConnectService.checkK3CloudParam(connectParam);
                if (!checkResult.isSuccess()) {
                    //校验参数失败
                    throw new ErpSyncDataException(
                            i18NStringManager.getByEi2(I18NStringEnum.s139, tenantId, checkResult.getErrMsg()),
                            null,
                            null);
                }
                break;
            case ERP_K3CLOUD_ULTIMATE:
                K3UltimateConnectParam k3UltimateConnectParam = (K3UltimateConnectParam) connectParam1;
                Result<Void> checkUltimateResult = erpConnectService.checkK3CloudUltimateParam(k3UltimateConnectParam);
                if (!checkUltimateResult.isSuccess()) {
                    //校验参数失败
                    throw ErpSyncDataException.wrap(checkUltimateResult.getErrCode(),
                            I18NStringEnum.s139,
                            tenantId,
                            checkUltimateResult.getErrMsg());
                }
                break;
            case ERP_U8:
                connectParam1.setBaseUrl("http://api.yonyouup.com");
                break;
            case ERP_DB_PROXY:
                DBProxyConnectParam dbProxyConnectParam = (DBProxyConnectParam) connectParam1;
                if (connectInfo.getId() != null) {
                    //原来的连接信息如果是空的，代表是新配置连接
                    ErpConnectInfoEntity oldEntity = erpConnectInfoDao.getByIdAndTenantId(tenantId, connectInfo.getId());
                    boolean isNew = StrUtil.isEmpty(oldEntity.getConnectParams());
                    boolean canGetServiceInfo = canGetServiceInfo(dbProxyConnectParam);
                    if (canGetServiceInfo) {
                        //>1.0版本 校验状态
                        Result<DbProxyServiceInfo> dbProxyServiceInfoResult = erpConnectService.getDbProxyServiceInfo(dbProxyConnectParam, tenantId);
                        if (!dbProxyServiceInfoResult.isSuccess()) {
                            throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s140.getI18nKey(),
                                    tenantId,
                                    i18NStringManager.getByEi2(I18NStringEnum.s140, tenantId, dbProxyServiceInfoResult.getErrMsg()),
                                    Lists.newArrayList(dbProxyServiceInfoResult.getErrMsg())),
                                    null,
                                    null);
                        }
                    } else {
                        if (isNew) {
                            //新增连接信息，1.0代理版本，直接返回错误
                            throw new ErpSyncDataException(I18NStringEnum.s141, tenantId);
                        }
                        //旧连接器不做检查
                    }
                }
                break;
            case STANDARD_CHANNEL:
                StandardConnectParam standardConnectParam = (StandardConnectParam) connectParam1;
                if (standardConnectParam.getServicePath() == null) {
                    standardConnectParam.setServicePath(new StandardConnectParam.ServicePath());
                }
                break;
            case ERP_JDY:
                JdyConnectParam jdyConnectParam = (JdyConnectParam) connectParam1;
                if (jdyConnectParam.getServicePath() == null) {
                    jdyConnectParam.setServicePath(new JdyConnectParam.ServicePath());
                }

                //更新函数的信息
                HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), -10000);
                //拿到配置信息
                FunctionServiceFindArg findArg = new FunctionServiceFindArg();
                findArg.setApiName(jdyConnectParam.getAplClassApiName());
                Result<FunctionServiceFindResult> functionFindRes = aplManager.findFunction(headerObj, findArg);
                if (functionFindRes.isSuccess() && ObjectUtils.isNotEmpty(functionFindRes.getData())) {
                    //避免页面修改的时候，没有同步信息到函数执行
                    FunctionServiceFindResult functionServiceFindResult = functionFindRes.getData();
                    //调用一下函数的信息
                    String body = CreateObjectEnum.updateDYFunc(jdyConnectParam, tenantId, functionServiceFindResult.getFunction().getBody());
                    String functionName = functionServiceFindResult.getFunction().getFunctionName();
                    String JDYApiname = functionServiceFindResult.getFunction().getApiName();
                    FunctionInfo functionInfo = FunctionInfo.builder().functionName(functionName)
                            .apiName(JDYApiname).
                            bindingObjectApiName("NONE").
                            nameSpace("erpdss-class").
                            type("class")
                            .remark(i18NStringManager.getByEi(I18NStringEnum.s3725, tenantId))
                            .body(body)
                            .build();
                    Result<FunctionServiceFindResult> function = aplManager.editFunction(headerObj, functionInfo);
                }
//                Result<String> tokenResult = jdyDataManager.executeToken(tenantId,connectInfo.getId(),jdyConnectParam.getAplClassApiName());
//                if(!tokenResult.isSuccess()){
//                    throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s575.getI18nKey(),
//                            tenantId,
//                            i18NStringManager.getByEi2(I18NStringEnum.s3685, tenantId, tokenResult.getErrMsg()),
//                            Lists.newArrayList(tokenResult.getErrMsg())),
//                            null,
//                            null);
//                }
                break;
            default:
        }
        return GsonUtil.toJson(connectParam1);
    }

    @Override
    public Result<Boolean> refreshUpdateTime(String tenantId, String dataCenterId) {
        if (StringUtils.isEmpty(dataCenterId)) return Result.newSuccess();

        ErpConnectInfoEntity entity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, dataCenterId);
        if (entity == null) return Result.newSuccess();
        entity.setUpdateTime(System.currentTimeMillis());
        int count = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(entity);
        return Result.newSuccess(count == 1);
    }

    @Override
    public Result<ErpConnectInfoEntity> getOrCreateCrmDc(String tenantId, String lang) {
        ErpConnectInfoEntity crmDc = erpConnectInfoManager.getOrCreateCrmDc(tenantId, lang);
        return Result.newSuccess(crmDc);
    }


    @Override
    public Result<String> getIconUrlByTempPath(String tenantId, NPathModel nPathModel) {
        final String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        String cPath = stoneFileManager.saveTempFile(ea, nPathModel.getPath());
        String url = ConfigCenter.CDN_DOMAIN + "/image/" + ea + "/" + cPath + "." + nPathModel.getExt();
        return Result.newSuccess(url);
    }

    @Override
    public Result<Void> updateConnectParams(String tenantId, String dataCenterId, String dataCenterName, String connectParams) {
        log.info("ConnectInfoServiceImpl.updateConnectParams,tenantId={},dataCenterId={},dataCenterName={},connectParams={}", tenantId, dataCenterId, dataCenterName, connectParams);
        ErpConnectInfoEntity erpConnectInfoEntity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .findById(dataCenterId);
        log.info("ConnectInfoServiceImpl.updateConnectParams,erpConnectInfoEntity={}", erpConnectInfoEntity);
        if (erpConnectInfoEntity == null) return Result.newError(ResultCodeEnum.CONNECT_INFO_NOT_EXISTS);

        if (dataCenterName != null) {
            erpConnectInfoEntity.setDataCenterName(dataCenterName);
        }
        erpConnectInfoEntity.setConnectParams(connectParams);
        erpConnectInfoEntity.setUpdateTime(System.currentTimeMillis());

        log.info("ConnectInfoServiceImpl.updateConnectParams,erpConnectInfoEntity2={}", erpConnectInfoEntity);
        int count = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .updateById(erpConnectInfoEntity);
        log.info("ConnectInfoServiceImpl.updateConnectParams,count={}", count);
        return count == 1 ? Result.newSuccess() : Result.newError(ResultCodeEnum.UPDATE_CONNECT_INFO_FAILED);
    }

    @Override
    public Result<ErpConnectInfoEntity> getDcInfo(String tenantId, String dataCenterId) {
        ErpConnectInfoEntity erpConnectInfoEntity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .findById(dataCenterId);
        log.info("ConnectInfoServiceImpl.getDcInfo,erpConnectInfoEntity={}", erpConnectInfoEntity);
        return Result.newSuccess(erpConnectInfoEntity);
    }

    @Override
    public Result<ErpConnectInfoEntity> getDcBind(GetDcBindArg arg) {
        ErpChannelEnum erpChannelEnum = ErpChannelEnum.valueOf(arg.getChannel());
        String tenantId = eieaConverter.enterpriseAccountToId(arg.getFsEa()) + "";
        List<ErpConnectInfoEntity> list = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getListDcByTenantId(tenantId, erpChannelEnum);
        if (CollectionUtils.isEmpty(list))
            return Result.newSuccess();

        for (ErpConnectInfoEntity entity : list) {
            if (StringUtils.isEmpty(entity.getConnectParams())) continue;
            QYWXConnectParam connectParam = JSONObject.parseObject(entity.getConnectParams(), QYWXConnectParam.class);
            if (connectParam == null) continue;
            if (StringUtils.equalsIgnoreCase(connectParam.getFsEa(), arg.getFsEa())
                    && StringUtils.equalsIgnoreCase(connectParam.getOutEa(), arg.getOutEa())) {
                return Result.newSuccess(entity);
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<ErpConnectInfoEntity> createConnector(CreateConnectorArg arg) {
        if (arg == null
                || StringUtils.isEmpty(arg.getId())
                || StringUtils.isEmpty(arg.getTenantId())
                || StringUtils.isEmpty(arg.getDataCenterName())
                || StringUtils.isEmpty(arg.getConnectParams())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ErpChannelEnum erpChannelEnum = ErpChannelEnum.valueOf(arg.getChannel());
        ErpConnectInfoEntity entity = new ErpConnectInfoEntity();
        entity.setId(arg.getId());
        entity.setTenantId(arg.getTenantId());
        entity.setChannel(erpChannelEnum);
        entity.setDataCenterName(arg.getDataCenterName());
        entity.setConnectParams(arg.getConnectParams());

        String fsEn = getEnterpriseName(arg.getTenantId());
        fsEn = StringUtils.isEmpty(fsEn) ? arg.getTenantId() : fsEn;
        entity.setEnterpriseName(fsEn);

        Integer connectorId = AllConnectorUtil.getByConnectorKey(arg.getChannel()).getConnectorId();
        int nextNum = erpConnectInfoManager.findNextNum(arg.getTenantId(), connectorId);
        log.info("ConnectInfoServiceImpl.createConnector,countByTenantIdAndChannel,nextNum={}", nextNum);
        entity.setNumber(nextNum);

        long time = System.currentTimeMillis();
        entity.setCreateTime(time);
        entity.setUpdateTime(time);
        log.info("ConnectInfoServiceImpl.createConnector,entity={}", entity);
        int insertResult = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(arg.getTenantId()))
                .insert(entity);
        log.info("ConnectInfoServiceImpl.createConnector,insertResult={}", insertResult);
        if (insertResult > 0) {
            ErpConnectInfoEntity erpConnectInfo = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(arg.getTenantId()))
                    .findById(entity.getId());
            log.info("ConnectInfoServiceImpl.createConnector,erpConnectInfo={}", erpConnectInfo);
            if (erpConnectInfo != null) {
                return Result.newSuccess(erpConnectInfo);
            }
        }
        return Result.newError(SYSTEM_ERROR);
    }

    @Override
    public Result<Void> deleteConnectInfo(String tenantId, Integer loginUserId, String dcId, String lang) {
        final ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        if (Objects.isNull(connectInfo)) {
            return Result.newError(ResultCodeEnum.CONNECT_INFO_NOT_EXISTS);
        }
        final int i = erpConnectInfoDao.deleteByTenantIdAndId(tenantId, dcId);
        if (i > 0) {
            // 记录审计日志
            final String msg = i18NStringManager.get2(I18NStringEnum.s3626, lang, tenantId, connectInfo.getDataCenterName(), String.valueOf(connectInfo.getNumber()));
            auditLogService.record(AuditLogI18nConstant.SubModuleEnum.ERPDSS_CONNECTION, AuditLogI18nConstant.BizOperationEnum.ERPDSS_DELETE_CONNECTION, connectInfo.getDataCenterName(), tenantId, String.valueOf(loginUserId), msg, 3);
            String actionId = "d_connect_" + tenantId + "_" + dcId;
            final ParallelUtils.ParallelTask backgroundTask = ParallelUtils.createBackgroundTask();
            // 对象,字段,集成流 放入回收站
            deleteObjectToRecycleBin(tenantId, dcId, backgroundTask, actionId);
            // 删除任务
            deleteTaskToRecycleBin(tenantId, dcId, backgroundTask, actionId);
            // 删除告警设置
            deleteAlarmRuleToRecycleBin(tenantId, dcId, backgroundTask, actionId);
            // 删除配置
            deleteConfigToRecycleBin(tenantId, dcId, backgroundTask, actionId);

            backgroundTask.run();
        }
        return Result.newSuccess();
    }

    @Override
    public Result<DataCenterInfoResult> initErpOrOADataCenterInfo(String tenantId, Integer userId, InitDataCenterInfoArg dataCenterInfoArg, String lang) {
        Connector connector = AllConnectorUtil.getByConnectorKey(dataCenterInfoArg.getConnectorKey());
        //内部连接器,走原来的逻辑
        ErpChannelEnum channel = connector.getChannel();
        DataCenterInfoResult dataCenterInfo = new DataCenterInfoResult();
        dataCenterInfo.setChannel(channel);
        dataCenterInfo.setConnectorKey(dataCenterInfoArg.getConnectorKey());
        dataCenterInfo.setDataCenterName(connector.getI18nName());
        if (ErpChannelEnum.OA.equals(channel)) {
            RLock lock = redisson.getLock("erpSyncData:" + "lockUCI" + tenantId);
            //插入的时候加锁
            if (lock.tryLock()) {
                try {
                    //oa进行连接器初始化的操作
                    oaConnParamService.initOAConnectInfo(tenantId, getEnterpriseName(tenantId));
                } finally {
                    lock.unlock();
                }
            } else {
                throw new ErpSyncDataException(I18NStringEnum.s136, tenantId);
            }
            com.fxiaoke.open.oasyncdata.result.base.Result<List<OAConnectInfoVO>> listResult = oaConnParamService.listInfoByTenantId(tenantId);
            if (listResult != null && CollectionUtils.isNotEmpty(listResult.getData())) {
                DataCenterInfoResult oaConnectInfo = new DataCenterInfoResult();
                oaConnectInfo.setChannel(channel);
                oaConnectInfo.setConnectorName(listResult.getData().get(0).getConnectParams().getConnectOaName());
                oaConnectInfo.setHasConnect(true);
                oaConnectInfo.setHasErpChannel(false);
                oaConnectInfo.setId(listResult.getData().get(0).getId());
                oaConnectInfo.setHasInited(true);
                oaConnectInfo.setConnectorType(oaConnectInfo.getChannel().getConnectorType());
                return Result.newSuccess(oaConnectInfo);
            }
        } else {//非oa连接器
            return this.updateDataCenterInfo(tenantId, userId, dataCenterInfo, lang);
        }

        return Result.newSuccess();
    }

    private void deleteConfigToRecycleBin(String tenantId, String dcId, ParallelUtils.ParallelTask backgroundTask, String actionId) {
        deleteToRecycleBin(tenantId, backgroundTask, actionId, RecycleType.TENANT_CONFIG,
                () -> {
                    final ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity();
                    entity.setTenantId(tenantId);
                    entity.setDataCenterId(dcId);
                    return tenantConfigurationManager.queryList(tenantId, entity);
                },
                () -> tenantConfigurationManager.deleteByTenantIdWithDataCenterId(tenantId, dcId));
    }

    private void deleteAlarmRuleToRecycleBin(String tenantId, String dcId, ParallelUtils.ParallelTask backgroundTask, String actionId) {
        // 告警规则
        deleteToRecycleBin(tenantId, backgroundTask, actionId, RecycleType.ERP_ALARM_RULE,
                () -> erpAlarmRuleDao.findData(tenantId, dcId, null, null, null, null),
                () -> erpAlarmRuleDao.deleteByDataCenterId(tenantId, dcId));

        // 告警信息
        deleteToRecycleBin(tenantId, backgroundTask, actionId, RecycleType.INTEGRATION_NOTIFICATION,
                maxId -> dataIntegrationNotificationDao.pageByDcId(tenantId, dcId, maxId, 1000),
                DataIntegrationNotificationEntity::getId,
                () -> dataIntegrationNotificationDao.deleteByDataCenterId(tenantId, dcId));
    }

    private void deleteTaskToRecycleBin(String tenantId, String dcId, ParallelUtils.ParallelTask backgroundTask, String actionId) {
        deleteToRecycleBin(tenantId, backgroundTask, actionId, RecycleType.HISTORY_DATA_TASK,
                () -> erpHistoryDataTaskDao.listByDataCenterId(tenantId, dcId),
                () -> erpHistoryDataTaskDao.deleteByDataCenterId(tenantId, dcId));
    }

    private void deleteObjectToRecycleBin(String tenantId, String dcId, ParallelUtils.ParallelTask backgroundTask, String actionId) {
        deleteToRecycleBin(tenantId, backgroundTask, actionId, RecycleType.ERP_OBJECT,
                () -> erpObjectDao.listByDcId(tenantId, dcId),
                () -> erpObjectDao.deleteByTenantIdAndDcId(tenantId, dcId));

        deleteToRecycleBin(tenantId, backgroundTask, actionId, RecycleType.ERP_OBJECT_RELATIONSHIP,
                () -> erpObjectRelationshipDao.listByTenantIdAndDataCenterId(tenantId, dcId),
                () -> erpObjectRelationshipDao.deleteByTenantIdAndDcId(tenantId, dcId));

        // 字段数量较大,需要分批处理
        deleteToRecycleBin(tenantId, backgroundTask, actionId, RecycleType.ERP_OBJECT_FIELD,
                maxId -> erpObjectFieldDao.pageByDcId(tenantId, dcId, maxId, 1000),
                ErpObjectFieldEntity::getId,
                () -> erpObjectFieldDao.deleteByTenantIdAndDcId(tenantId, dcId));

        deleteToRecycleBin(tenantId, backgroundTask, actionId, RecycleType.ERP_FIELD_EXTEND,
                maxId -> erpFieldExtendDao.pageByDcId(tenantId, dcId, maxId, 1000),
                ErpFieldExtendEntity::getId,
                () -> erpFieldExtendDao.deleteByTenantIdAndDataCenterId(tenantId, dcId));

        deleteToRecycleBin(tenantId, backgroundTask, actionId, RecycleType.PLOY_STREAM,
                () -> {
                    final List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.queryByDcId(tenantId, dcId, null);
                    syncPloyDetailEntities.stream()
                            .map(SyncPloyDetailEntity::getId)
                            .forEach(syncPloyDetailId -> adminSyncPloyDetailSnapshotDao.updateStatusByPloyDetailId(syncPloyDetailId, SyncPloyDetailStatusEnum.DISABLE.getStatus()));
                    return syncPloyDetailEntities;
                },
                () -> adminSyncPloyDetailDao.deleteByTenantAndDcId(tenantId, dcId));
    }

    private <T> void deleteToRecycleBin(String tenantId, ParallelUtils.ParallelTask backgroundTask, String actionId, RecycleType type, Supplier<List<T>> supplier, Runnable delete) {
        backgroundTask.submit(() -> {
            final List<T> list = supplier.get();
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            recycleBinDao.batchInsertJson(tenantId, type, list, actionId);
            delete.run();
        });
    }


    private <T, V> void deleteToRecycleBin(String tenantId, ParallelUtils.ParallelTask backgroundTask, String actionId, RecycleType type, Function<V, List<T>> function, Function<T, V> idFunction, Runnable delete) {
        backgroundTask.submit(() -> {
            V max = null;
            while (true) {
                List<T> list = function.apply(max);
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }
                recycleBinDao.batchInsertJson(tenantId, type, list, actionId);
                max = idFunction.apply(list.get(list.size() - 1));
            }
            delete.run();
        });
    }

    @Override
    public Result<Void> updateStandardConnectSysName(StandardConnectParam connectParam, String tenantId, Integer userId, String dataCenterId, String lang) {
        //查询连接器信息
        ErpConnectInfoEntity erpConnectInfoEntity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .findById(dataCenterId);
        if(ObjectUtils.isEmpty(erpConnectInfoEntity)) {
            return Result.newError(ResultCodeEnum.CONNECT_INFO_NOT_EXISTS);
        }

        //替换
        StandardConnectParam standardConnectParam = GsonUtil.fromJson(erpConnectInfoEntity.getConnectParams(), StandardConnectParam.class);
        standardConnectParam.setSystemName(connectParam.getSystemName());
        erpConnectInfoEntity.setConnectParams(GsonUtil.toJson(standardConnectParam));

        //更新
        erpConnectInfoEntity.setUpdateTime(System.currentTimeMillis());
        int updateResult = erpConnectInfoManager.updateById(tenantId, erpConnectInfoEntity);
        if (updateResult != 1) {
            return Result.newError(SYSTEM_ERROR);
        }
        return Result.newSuccess();
    }
}
