package com.fxiaoke.open.erpsyncdata.admin.model.excel;

import com.fxiaoke.open.erpsyncdata.common.i18n.I18NModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


public class ExportSyncDataMapping {

    @Data
    @ApiModel("数据映射导出查询参数")
    @ToString(exclude = "file")
    public static class ExportSyncDataMappingArg extends I18NModel {
        private static  long serialVersionUID = -8761645379239188525L;

        @ApiModelProperty("企业id")
        private String tenantId;


        @ApiModelProperty("登陆用户Id")
        private Integer userId;

        @ApiModelProperty("数据中心Id")
        private String dataCenterId;

        @ApiModelProperty("策略明细id")
        @NotNull
        private String ployDetailId;

        @ApiModelProperty("按状态筛选")
        private Integer status;
        @ApiModelProperty("源数据dataId")
        private String sourceDataId;
        @ApiModelProperty("目标数据id")
        private String destDataId;
        @ApiModelProperty("源数据dataName")
        private String sourceDataName;
        @ApiModelProperty("目标数据dataId")
        private String destDataName;
        @ApiModelProperty("状态详情")
        private String remark;
        @ApiModelProperty("id")
        private String id;
        @ApiModelProperty("lastSyncDataId")
        private String lastSyncDataId;
        @ApiModelProperty("sourceObjectApiName")
        private String sourceObjectApiName;

//        @ApiModelProperty("搜索内容")
//        private String searchText;

        @ApiModelProperty(value = "查询开始时间")
        @NotNull
        private Long startTime;

        @ApiModelProperty(value = "查询结束时间")
        @NotNull
        private Long endTime;

    }

    @Data
    @ApiModel("导出excel文件结果")
    public static class Result implements Serializable {
        private static final long serialVersionUID = 9181946463285972887L;

        private Boolean success=false;

        private String printMsg;

        private String downloadUrl;


    }


    @Data
    public static class PageExcel {
        List<ExportDataMappingExcelVo> dataForExcel;
        Long updateTime;
        String maxId;
        boolean next;
    }


}
