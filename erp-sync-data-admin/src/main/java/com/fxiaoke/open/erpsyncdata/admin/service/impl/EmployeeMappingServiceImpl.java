package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.fxiaoke.erpdss.connector.core.model.ConnectorFeature;
import com.fxiaoke.open.erpsyncdata.admin.service.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ConnectorManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorHandlerFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpDataService;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.PageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryEmployeeMappingListArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.EmployeeData;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SimpleEmployeeMapping;
import com.fxiaoke.open.erpsyncdata.preprocess.result.EmployeeMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.SpecialFieldMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ExceptionUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AuditLogService;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.SYSTEM_ERROR;

/**
 * <AUTHOR>
 * @Date: 10:03 2020/9/3
 * @Desc:
 */
@Slf4j
@Service
@Data
public class EmployeeMappingServiceImpl implements EmployeeMappingService {

    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private FsObjectDataService fsObjectDataService;
    @Autowired
    private ErpDataService erpDataService;
    @Autowired
    private ConnectInfoService connectInfoService;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ApiClientHolder apiClientHolder;
    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private SystemFieldService systemFieldService;
    @Autowired
    private AuditLogService auditLogService;
    @Autowired
    private ErpObjectService erpObjectService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private CommonBusinessManager commonBusinessManager;
    @Autowired
    private ConnectorManager connectorManager;

    @Override
    public Result<QueryResult<List<EmployeeMappingResult>>> queryEmployeeMapping(String tenantId,
                                                                                 int userId,
                                                                                 String dataCenterId,
                                                                                 QueryEmployeeMappingListArg pageArg,
                                                                                 String lang) {
        QueryResult<List<EmployeeMappingResult>> queryResult = new QueryResult<>();
        queryResult.setPageNum(pageArg.getPageNum());
        queryResult.setPageSize(pageArg.getPageSize());
        Integer total = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).countByTenantIdAndDataTypeAndDcId(tenantId, dataCenterId, ErpFieldTypeEnum.employee, pageArg.getIsBind(), pageArg.getQueryStr());
        queryResult.setTotal(total);
        if (total == 0) {
            queryResult.setDataList(Lists.newArrayList());
            return Result.newSuccess(queryResult);
        }
        List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities =
                erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantIdAndDataTypeAndDcId(tenantId, dataCenterId, ErpFieldTypeEnum.employee, queryResult.getPageSize(),
                        (pageArg.getPageNum() - 1) * pageArg.getPageSize(), pageArg.getIsBind(), pageArg.getQueryStr());
        if (CollectionUtils.isEmpty(erpFieldDataMappingEntities)) {
            queryResult.setDataList(Lists.newArrayList());
            return Result.newSuccess(queryResult);
        }
        List<EmployeeMappingResult> employeeMappingResults = Lists.newArrayList();
        for (ErpFieldDataMappingEntity erpFieldDataMappingEntity : erpFieldDataMappingEntities) {
            EmployeeMappingResult employeeMappingResult = GsonUtil.fromJson(erpFieldDataMappingEntity.getFieldDataExtendValue(),
                    EmployeeMappingResult.class);
            employeeMappingResult.setErpUserAccountMsg(employeeMappingResult.getErpUserAccount());
            if (ErpChannelEnum.ERP_K3CLOUD.equals(employeeMappingResult.getChannel())
                    && StringUtils.isNotBlank(employeeMappingResult.getErpUserAccount())
                    && StringUtils.isBlank(employeeMappingResult.getErpUserId())) {
                employeeMappingResult.setErpUserAccountMsg(i18NStringManager.get2(I18NStringEnum.s345.getI18nKey(),
                        lang,
                        tenantId,
                        i18NStringManager.getByEi2(I18NStringEnum.s345, tenantId, employeeMappingResult.getErpUserAccount()),
                        Lists.newArrayList(employeeMappingResult.getErpUserAccount())));
            }
            employeeMappingResults.add(employeeMappingResult);
        }
        queryResult.setDataList(employeeMappingResults);
        return Result.newSuccess(queryResult);
    }

    @Override
    public Result<QueryResult<List<SpecialFieldMappingResult.EmpResult>>> queryEmpOrUserMapping(String tenantId,
                                                                                                int userId,
                                                                                                String dataCenterId,
                                                                                                QueryEmployeeMappingListArg pageArg) {
        QueryResult<List<SpecialFieldMappingResult.EmpResult>> queryResult = new QueryResult<>();
        queryResult.setPageNum(pageArg.getPageNum());
        queryResult.setPageSize(pageArg.getPageSize());
        int total = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .countByTenantIdAndDataTypeAndDcId(tenantId, dataCenterId, pageArg.getFieldType(), pageArg.getIsBind(), pageArg.getQueryStr());
        queryResult.setTotal(total);
        if (total == 0) {
            queryResult.setDataList(Lists.newArrayList());
            return Result.newSuccess(queryResult);
        }
        List<ErpFieldDataMappingEntity> userMappingEntityList = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listByTenantIdAndDataTypeAndDcId(tenantId,
                        dataCenterId,
                        pageArg.getFieldType(),
                        queryResult.getPageSize(),
                        (pageArg.getPageNum() - 1) * pageArg.getPageSize(),
                        pageArg.getIsBind(),
                        pageArg.getQueryStr());
        List<SpecialFieldMappingResult.EmpResult> resultList = BeanUtil.copyList(userMappingEntityList, SpecialFieldMappingResult.EmpResult.class);
        queryResult.setDataList(resultList);
        //获取员工状态
        try {
            //实时查询员工
            Map<Integer, SpecialFieldMappingResult.EmpResult> fsUserIdMap = resultList.stream()
                    .filter(v -> NumberUtil.isInteger(v.getFsDataId()))
                    .collect(Collectors.toMap(v -> Integer.parseInt(v.getFsDataId()), v -> v, (u, v) -> u));
            if (!fsUserIdMap.isEmpty()) {
                Result<List<EmployeeDto>> listResult = fsObjectDataService.queryEmployeeByFsUserId(Integer.valueOf(tenantId), fsUserIdMap.keySet());
                if (listResult.isSuccess()) {
                    listResult.getData().forEach(e -> {
                        fsUserIdMap.get(e.getEmployeeId()).setFsUserStatus(e.getStatus().getValue());
                    });
                }
            }
        } catch (Exception e) {
            log.error("queryEmpOrUserMapping set fs user status error", e);
        }
        return Result.newSuccess(queryResult);
    }

    @Override
    public Result<String> bindEmployeeMappingByFsId(String tenantId,
                                                    int userId,
                                                    EmployeeMappingResult employeeMappingResult,
                                                    boolean returnErrorWhenPostProcessFailed,
                                                    boolean deleteByFsIdWhenPostProcessFailed) {
        log.info("EmployeeMappingServiceImpl.bindEmployeeMappingByFsId,employeeMappingResult={}", employeeMappingResult);
        if (StringUtils.isNotEmpty(employeeMappingResult.getErpUserAccount())) {
            String userAccountId = getErpUserId(employeeMappingResult.getErpUserAccount(), tenantId, employeeMappingResult.getChannel(), employeeMappingResult.getDataCenterId());
            log.info("EmployeeMappingServiceImpl.bindEmployeeMappingByFsId,userAccountId={}", userAccountId);
            if (ErpChannelEnum.ERP_K3CLOUD.equals(employeeMappingResult.getChannel()) && StringUtils.isBlank(userAccountId)) {
                return Result.newError(ResultCodeEnum.THE_USERNAME_UNKNOWN);
            }
            employeeMappingResult.setErpUserId(userAccountId);
        }
        //填充信息
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, employeeMappingResult.getDataCenterId());
        if (connectInfo == null) {
            return Result.newError(ResultCodeEnum.CONNECT_INFO_NOT_EXISTS);
        }
        if (employeeMappingResult.getChannel() == null) {
            employeeMappingResult.setChannel(connectInfo.getChannel());
        }
        employeeMappingResult.setErpEmployeePhone(StringUtils.trim(employeeMappingResult.getErpEmployeePhone()));
        Integer fsEmployeeId = employeeMappingResult.getFsEmployeeId();
        if (fsEmployeeId != null) {
            Result<List<EmployeeDto>> fsEmployees = fsObjectDataService.queryEmployeeByFsUserId(Integer.valueOf(tenantId), Lists.newArrayList(fsEmployeeId));
            if (!fsEmployees.isSuccess()) {
                return Result.copy(fsEmployees);
            }
            if (CollUtil.isEmpty(fsEmployees.getData())) {
                return Result.newError(ResultCodeEnum.GET_FS_EMPLOYEE_FAILED);
            }
            EmployeeDto employeeDto = fsEmployees.getData().get(0);
            employeeMappingResult.setFsEmployeeName(employeeDto.getName());
            employeeMappingResult.setFsEmployeePhone(employeeDto.getMobile());
            employeeMappingResult.setFsEmployeeStatus(employeeDto.getStatus().getValue());

            if (employeeMappingResult.getId() == null) {
                //尝试根据fsUserId查找出mapping
                List<ErpFieldDataMappingEntity> oldEntry = erpFieldDataMappingDao.listNoSearch2(tenantId, employeeMappingResult.getDataCenterId(), ErpFieldTypeEnum.employee, String.valueOf(fsEmployeeId), null);
                if (CollectionUtils.isNotEmpty(oldEntry)) {
                    employeeMappingResult.setId(oldEntry.get(0).getId());
                }
            }
        }
        log.info("EmployeeMappingServiceImpl.bindEmployeeMappingByFsId,employeeMappingResult2={}", employeeMappingResult);
        Result<String> updateResult = updateEmployeeMapping(tenantId, employeeMappingResult);
        if (updateResult.isSuccess()) {
            return postProcess(tenantId, employeeMappingResult, returnErrorWhenPostProcessFailed, deleteByFsIdWhenPostProcessFailed, connectInfo);
        } else {
            return Result.copy(updateResult);
        }
    }

    @NotNull
    private Result<String> postProcess(String tenantId, EmployeeMappingResult employeeMappingResult, boolean returnErrorWhenPostProcessFailed, boolean deleteByFsIdWhenPostProcessFailed, ErpConnectInfoEntity connectInfo) {
        Result<Void> postProcessResult;
        //后动作
        try {
            ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(connectInfo.getChannel(), connectInfo.getConnectParams());
            postProcessResult = erpDataManager.processChangeEmployeeMapping(employeeMappingResult, connectInfo);
        } catch (Exception e) {
            log.warn("processChangeEmployeeMapping error", e);
            postProcessResult = Result.newError(ExceptionUtil.getMessage(e));
        }
        if (!postProcessResult.isSuccess()) {
            if (deleteByFsIdWhenPostProcessFailed) {
                //删除数据
                deleteEmployeeMappingByFsUserId(tenantId, employeeMappingResult);
            }
            if (returnErrorWhenPostProcessFailed) {
                //postProcess失败时返回错误
                return Result.copy(postProcessResult);
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> updateEmpOrUserMapping(String tenantId,
                                                 int userId,
                                                 ErpFieldDataMappingEntity mappingEntity,
                                                 String lang) {
        if (StringUtils.isEmpty(mappingEntity.getFsDataId())) {
            return Result.newErrorByI18N(ResultCodeEnum.PARAM_ERROR,
                    I18NStringEnum.s2215.getI18nValue(),
                    I18NStringEnum.s2215.getI18nKey(),
                    null);
        }
        Result<List<EmployeeDto>> result = fsObjectDataService.queryEmployeeByFsUserId(Integer.valueOf(tenantId),
                Lists.newArrayList(Integer.valueOf(mappingEntity.getFsDataId())));
        if (!result.isSuccess())
            return Result.newSystemError( I18NStringEnum.s346);
        List<EmployeeDto> data = result.getData();
        EmployeeDto employeeDto;
        if (CollUtil.isEmpty(data)) {
            employeeDto = new EmployeeDto();
            employeeDto.setName(i18NStringManager.get2(I18NStringEnum.s347,
                    lang,
                    tenantId,
                    mappingEntity.getFsDataName()));
            employeeDto.setEmployeeId(Integer.parseInt(mappingEntity.getFsDataId()));
            employeeDto.setStatus(EmployeeEntityStatus.DELETE);
        } else {
            employeeDto = data.get(0);
        }
        String name = employeeDto.getName();

        ErpFieldDataMappingEntity entity = new ErpFieldDataMappingEntity();
        entity.setId(mappingEntity.getId());
        entity.setTenantId(tenantId);
        entity.setDataCenterId(mappingEntity.getDataCenterId());
        entity.setChannel(mappingEntity.getChannel());
        entity.setDataType(mappingEntity.getDataType());
        entity.setFsDataId(mappingEntity.getFsDataId());
        entity.setFsDataName(name);
        entity.setErpDataId(mappingEntity.getErpDataId());
        entity.setErpDataName(mappingEntity.getErpDataName());

        if (mappingEntity.getDataType() == ErpFieldTypeEnum.user) {
            String erpUserId = getErpUserId(mappingEntity.getErpDataName(), tenantId, mappingEntity.getChannel(), mappingEntity.getDataCenterId());
            log.info("EmployeeMappingServiceImpl.updateEmpOrUserMapping,erpUserId={}", erpUserId);
            if (ErpChannelEnum.ERP_K3CLOUD.equals(mappingEntity.getChannel()) && StringUtils.isBlank(erpUserId)) {
                return Result.newError(ResultCodeEnum.THE_USERNAME_UNKNOWN);
            }
            entity.setErpDataId(erpUserId);
        }

        long timestamp = System.currentTimeMillis();
        entity.setCreateTime(timestamp);
        entity.setUpdateTime(timestamp);

        //todo
        //兼容历史数据，VIP环境发布后，这个需要去掉
        EmployeeMappingResult employeeMappingResult = null;
        if (mappingEntity.getDataType() == ErpFieldTypeEnum.employee) {
            employeeMappingResult = new EmployeeMappingResult();
            employeeMappingResult.setId(mappingEntity.getId());
            employeeMappingResult.setChannel(mappingEntity.getChannel());
            employeeMappingResult.setDataCenterId(mappingEntity.getDataCenterId());
            employeeMappingResult.setFsEmployeeId(Integer.valueOf(mappingEntity.getFsDataId()));
            employeeMappingResult.setFsEmployeeName(name);
            employeeMappingResult.setFsEmployeePhone(employeeDto.getMobile());
            employeeMappingResult.setFsEmployeeStatus(employeeDto.getStatus().getValue());
            employeeMappingResult.setErpEmployeeId(mappingEntity.getErpDataId());
            employeeMappingResult.setErpEmployeeName(mappingEntity.getErpDataName());
            employeeMappingResult.setErpEmployeePhone(null);
            employeeMappingResult.setErpEmployeeAccount(null);
        }

        Result<String> ifExist = ifExist(entity);
        if (!ifExist.isSuccess()) {
            return ifExist;
        }

        if (StringUtils.isEmpty(mappingEntity.getId())) {
            entity.setId(idGenerator.get());
            //新增员工数据前，需要补全关联的用户数据
            if (mappingEntity.getDataType() == ErpFieldTypeEnum.employee) {
                employeeMappingResult.setId(entity.getId());
                //查找员工关联的用户数据，并更新员工数据的fieldDataExtendValue字段
                List<ErpFieldDataMappingEntity> userList = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .listNoSearch(tenantId, mappingEntity.getDataCenterId(), ErpFieldTypeEnum.user, mappingEntity.getFsDataId(), null);
                if (CollectionUtils.isNotEmpty(userList)) {
                    employeeMappingResult.setErpUserId(userList.get(0).getErpDataId());
                    employeeMappingResult.setErpUserAccount(userList.get(0).getErpDataName());
                }
                entity.setFieldDataExtendValue(GsonUtil.toJson(employeeMappingResult));
            }
            int insert = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .insert(entity);
            if (insert != 1) {
                log.info("EmployeeMappingServiceImpl.bindUserMapping,insert failed,entity={}", entity);
                return Result.newSystemError( I18NStringEnum.s348);
            }
            //新增用户数据成功，需要更新用户关联的员工fieldDataExtendValue字段
            if (mappingEntity.getDataType() == ErpFieldTypeEnum.user) {
                //查找用户关联的员工数据，并更新员工数据的fieldDataExtendValue字段
                List<ErpFieldDataMappingEntity> empList = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .listNoSearch(tenantId, mappingEntity.getDataCenterId(), ErpFieldTypeEnum.employee, mappingEntity.getFsDataId(), null);
                if (CollectionUtils.isNotEmpty(empList)) {
                    ErpFieldDataMappingEntity empEntity = empList.get(0);
                    String fieldDataExtendValue = empEntity.getFieldDataExtendValue();
                    if (StringUtils.isNotEmpty(fieldDataExtendValue)) {
                        EmployeeMappingResult empMappingResult = GsonUtil.fromJson(fieldDataExtendValue, EmployeeMappingResult.class);
                        if (empMappingResult != null && StringUtils.isNotEmpty(empMappingResult.getId())) {
                            empMappingResult.setErpUserId(entity.getErpDataId());
                            empMappingResult.setErpUserAccount(entity.getErpDataName());
                            empEntity.setFieldDataExtendValue(GsonUtil.toJson(empMappingResult));
                            int count = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                                    .updateById(empEntity);
                            if (count != 1) {
                                log.info("EmployeeMappingServiceImpl.updateEmpOrUserMapping,update failed,empEntity={}", empEntity);
                                return Result.newSystemError( I18NStringEnum.s349);
                            }
                        }
                    }
                }
            }
        } else {
            //更新员工数据前，需要补全关联的用户数据
            if (mappingEntity.getDataType() == ErpFieldTypeEnum.employee) {
                employeeMappingResult.setId(entity.getId());
                //查找员工关联的用户数据，并更新员工数据的fieldDataExtendValue字段
                List<ErpFieldDataMappingEntity> userList = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .listNoSearch(tenantId, mappingEntity.getDataCenterId(), ErpFieldTypeEnum.user, mappingEntity.getFsDataId(), null);
                if (CollectionUtils.isNotEmpty(userList)) {
                    employeeMappingResult.setErpUserId(userList.get(0).getErpDataId());
                    employeeMappingResult.setErpUserAccount(userList.get(0).getErpDataName());
                }
                entity.setFieldDataExtendValue(GsonUtil.toJson(employeeMappingResult));
            }
            int count = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .updateById(entity);
            if (count != 1) {
                log.info("EmployeeMappingServiceImpl.updateEmpOrUserMapping,update failed,entity={}", entity);
                return Result.newSystemError( I18NStringEnum.s350);
            }
            //更新用户数据成功，需要更新用户关联的员工fieldDataExtendValue字段
            if (mappingEntity.getDataType() == ErpFieldTypeEnum.user) {
                //查找用户关联的员工数据，并更新员工数据的fieldDataExtendValue字段
                List<ErpFieldDataMappingEntity> empList = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .listNoSearch(tenantId, mappingEntity.getDataCenterId(), ErpFieldTypeEnum.employee, mappingEntity.getFsDataId(), null);
                if (CollectionUtils.isNotEmpty(empList)) {
                    ErpFieldDataMappingEntity empEntity = empList.get(0);
                    String fieldDataExtendValue = empEntity.getFieldDataExtendValue();
                    if (StringUtils.isNotEmpty(fieldDataExtendValue)) {
                        EmployeeMappingResult empMappingResult = GsonUtil.fromJson(fieldDataExtendValue, EmployeeMappingResult.class);
                        if (empMappingResult != null && StringUtils.isNotEmpty(empMappingResult.getId())) {
                            empMappingResult.setErpUserId(entity.getErpDataId());
                            empMappingResult.setErpUserAccount(entity.getErpDataName());
                            empEntity.setFieldDataExtendValue(GsonUtil.toJson(empMappingResult));
                            int count2 = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                                    .updateById(empEntity);
                            if (count2 != 1) {
                                log.info("EmployeeMappingServiceImpl.updateEmpOrUserMapping,update failed,empEntity={}", empEntity);
                                return Result.newSystemError( I18NStringEnum.s349);
                            }
                        }
                    }
                }
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<QueryResult<List<EmployeeMappingResult>>> batchDeleteEmployeeMapping(String tenantId,
                                                                                       int userId,
                                                                                       String dataCenterId,
                                                                                       List<String> idList,
                                                                                       String lang) {
        int total = 0;
        for (String id : idList) {
            int count = erpFieldDataMappingDao
                    .setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .deleteByEiAndId(tenantId, id);
            total += count;
        }

        if (total > 0) {
            auditLogService.recordDeleteSystemFieldLog(ErpFieldTypeEnum.employee,
                    tenantId,
                    userId + "",
                    i18NStringManager.get(I18NStringEnum.s351, lang, tenantId));
            return queryEmployeeMapping(tenantId, userId, dataCenterId, new QueryEmployeeMappingListArg(), lang);
        } else {
            auditLogService.recordDeleteSystemFieldLog(ErpFieldTypeEnum.employee,
                    tenantId,
                    userId + "",
                    i18NStringManager.get(I18NStringEnum.s352, lang, tenantId));
            return Result.newError(SYSTEM_ERROR);
        }
    }

    @Override
    public Result<Integer> batchDeleteEmpOrUserMapping(String tenantId,
                                                       int userId,
                                                       String dataCenterId,
                                                       ErpFieldTypeEnum fieldType,
                                                       List<String> idList,
                                                       String lang) {
        int count = 0;
        if (CollectionUtils.isEmpty(idList)) {
            count = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .deleteByDataType(tenantId, dataCenterId, fieldType);
        } else {
            count = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .batchDeleteByIds(tenantId, idList);
        }

        String msg = i18NStringManager.get(I18NStringEnum.s353, lang, tenantId);
        if (fieldType == ErpFieldTypeEnum.employee) {
            msg = i18NStringManager.get(I18NStringEnum.s354, lang, tenantId);
        } else if (fieldType == ErpFieldTypeEnum.user) {
            msg = i18NStringManager.get(I18NStringEnum.s355, lang, tenantId);
        }

        if (count > 0) {
            auditLogService.recordDeleteSystemFieldLog(fieldType,
                    tenantId,
                    userId + "",
                    i18NStringManager.get2(I18NStringEnum.s356, lang, tenantId, msg));
        } else {
            auditLogService.recordDeleteSystemFieldLog(fieldType,
                    tenantId,
                    userId + "",
                    i18NStringManager.get2(I18NStringEnum.s357, lang, tenantId, msg));
        }
        return Result.newSuccess(count);
    }

    private Result<List<EmployeeMappingResult>> queryAllUnSyncData(String tenantId, int userId, String dataCenterId) {

        List<ErpFieldDataMappingEntity> boundEmployees = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listNoSearch2(tenantId, dataCenterId
                , ErpFieldTypeEnum.employee, null, null);
        List<String> bindingErpIds = Lists.newArrayList();
        for (ErpFieldDataMappingEntity erpFieldDataMappingEntity : boundEmployees) {
            if (StringUtils.isNotBlank(erpFieldDataMappingEntity.getFieldDataExtendValue())) {
                EmployeeMappingResult employeeMappingResult = GsonUtil.fromJson(erpFieldDataMappingEntity.getFieldDataExtendValue(),
                        EmployeeMappingResult.class);
                bindingErpIds.add(employeeMappingResult.getErpEmployeeId());
            } else if (StringUtils.isNotBlank(erpFieldDataMappingEntity.getErpDataId())) {
                bindingErpIds.add(erpFieldDataMappingEntity.getErpDataId());
            }
        }
        Result<List<EmployeeMappingResult>> erpEmployeesResult = queryAllErpEmployee(tenantId, userId, dataCenterId);
        if (!erpEmployeesResult.isSuccess()) {
            return Result.copy(erpEmployeesResult);
        }
        List<EmployeeMappingResult> employees = Lists.newArrayList();
        if (erpEmployeesResult != null && CollectionUtils.isNotEmpty(erpEmployeesResult.getData())) {
            List<EmployeeMappingResult> erpEmployees = erpEmployeesResult.getData();
            //增加抛出id重复的异常
            Map<String, EmployeeMappingResult> erpId2MappingMap =
                    erpEmployees.stream().collect(Collectors.toMap(EmployeeMappingResult::getErpEmployeeId, mapping -> mapping,
                            (u, v) -> {
                                throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s156.getI18nKey(),
                                        tenantId,
                                        i18NStringManager.getByEi2(I18NStringEnum.s156, tenantId,
                                                u.getErpEmployeeId(), u.getErpEmployeeName(), v.getErpEmployeeId(), v.getErpEmployeeName()),
                                        Lists.newArrayList(u.getErpEmployeeId(), u.getErpEmployeeName(), v.getErpEmployeeId(), v.getErpEmployeeName())),
                                        null,
                                        null);
                            }));
            bindingErpIds.forEach(erpId2MappingMap::remove);
            employees = Lists.newArrayList(erpId2MappingMap.values());
        }


        return Result.newSuccess(employees);
    }


    @Override
    public Result<QueryResult<List<EmployeeMappingResult>>> queryAllUnSyncEmployee(String tenantId, int userId, String dataCenterId, PageArg arg) {
        QueryResult<List<EmployeeMappingResult>> queryResult = new QueryResult<>();
        queryResult.setPageNum(arg.getPageNum());
        queryResult.setPageSize(arg.getPageSize());

        Result<List<EmployeeMappingResult>> employeesRes = queryAllUnSyncData(tenantId, userId, dataCenterId);
        if (!employeesRes.isSuccess()) {
            return Result.copy(employeesRes);
        }
        List<EmployeeMappingResult> employees = employeesRes.getData();
        if (StringUtils.isNotEmpty(arg.getQueryStr())) {
            employees = employees.stream().filter(t -> {
                boolean flag = false;
                if (StringUtils.isNotEmpty(t.getErpEmployeeName())) {
                    flag = t.getErpEmployeeName().contains(arg.getQueryStr());
                }
                if (!flag && StringUtils.isNotEmpty(t.getErpEmployeeId())) {
                    flag = t.getErpEmployeeId().contains(arg.getQueryStr());
                }
                if (!flag && StringUtils.isNotEmpty(t.getFsEmployeeName())) {
                    flag = t.getFsEmployeeName().contains(arg.getQueryStr());
                }
                return flag;
            }).collect(Collectors.toList());
        }

        queryResult.setTotal(employees.size());
        List<List<EmployeeMappingResult>> pageEmployees = ListUtils.partition(employees, arg.getPageSize());
        if (arg.getPageNum() <= pageEmployees.size()) {
            queryResult.setDataList(pageEmployees.get(arg.getPageNum() - 1));
        } else {
            queryResult.setDataList(Lists.newArrayList());
        }
        return Result.newSuccess(queryResult);
    }

    @Override
    public Result<List<EmployeeMappingResult>> queryAllErpEmployee(String tenantId, int userId, String dataCenterId) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        Long connectorConfigFeatures = connectorManager.getConnectorFeatures(tenantId, dataCenterId);
        if (connectorConfigFeatures != null && ConnectorFeature.supportQueryEmp.isEnabled(connectorConfigFeatures)) {
            //连接器支持获取员工信息
            //从连接器调用接口
            ConnectorDataHandler dataHandler = ConnectorHandlerFactory.getDataHandler(connectInfo.getChannel(), connectInfo.getConnectParams());
            Result<List<EmployeeData>> listResult = dataHandler.queryAllEmployee(connectInfo);
            if (!listResult.isSuccess()) {
                return Result.copy(listResult);
            }
            List<EmployeeMappingResult> mappingResultList = listResult.getData().stream().map(t -> {
                EmployeeMappingResult result = new EmployeeMappingResult();
                result.setErpEmployeeId(t.getId());
                result.setErpEmployeeName(t.getName());
                result.setErpEmployeePhone(t.getPhone());
                return result;
            }).collect(Collectors.toList());
            return Result.newSuccess(mappingResultList);
        }
        TimeFilterArg arg = new TimeFilterArg();
        arg.setStartTime(0L);
        arg.setEndTime(System.currentTimeMillis());
        arg.setTenantId(tenantId);
        arg.setOperationType(EventTypeEnum.SYNC_MENU.getType());
        arg.setOffset(0);
        arg.setLimit(2000);//没有做分页，默认最多2000个
        Result<List<EmployeeMappingResult>> result;
        switch (connectInfo.getChannel()) {
            case ERP_U8:
                arg.setObjAPIName(ObjectApiNameEnum.U8_EMPLOYEE.getObjApiName());
                return queryAllU8Employee(arg, dataCenterId);
            case ERP_U8_EAI:
                arg.setObjAPIName(ObjectApiNameEnum.U8_EAI_EMPLOYEE.getObjApiName());
                return queryAllU8EaiEmployee(arg, dataCenterId);
            case ERP_K3CLOUD:
                arg.setObjAPIName(ObjectApiNameEnum.K3CLOUD_EMPLOYEE.getObjApiName());
                return queryAllK3CloudEmployee(arg, dataCenterId);
            case ERP_SAP:
            case STANDARD_CHANNEL:
            case ERP_DB_PROXY:
                arg.setObjAPIName(ObjectApiNameEnum.SAP_EMPLOYEE.getObjApiName());
                return queryAllSapEmployee(arg, dataCenterId);
            default:
                log.info("channel not support: " + connectInfo.getChannel());
        }

        return Result.newSuccess(Lists.newArrayList());
    }

    private Result<List<EmployeeMappingResult>> queryAllU8Employee(TimeFilterArg arg, String dataCenterId) {
        List<EmployeeMappingResult> results = Lists.newArrayList();
        //不入库
        Result<StandardListData> StandardListDataRes = erpDataService.listStandardErpObjDataByTime(arg, dataCenterId);
        if (StandardListDataRes.isSuccess()) {
            StandardListData standardListData = StandardListDataRes.getData();
            for (StandardData standardData : standardListData.getDataList()) {
                if (standardData != null && standardData.getMasterFieldVal() != null) {
                    ObjectData objectData = standardData.getMasterFieldVal();
                    if (objectData.get("cpsnmobilephone") == null) {//电话为空的过滤掉
                        continue;
                    }
                    EmployeeMappingResult employeeMappingResult = new EmployeeMappingResult();
                    employeeMappingResult.setErpEmployeePhone(String.valueOf(objectData.get("cpsnmobilephone")));
                    employeeMappingResult.setErpEmployeeName(String.valueOf(objectData.get("name")));
                    employeeMappingResult.setErpEmployeeId(String.valueOf(objectData.get("code")));
                    //employeeMappingResult.setErpEmployeeAccount(String.valueOf(objectData.get("")));
                    employeeMappingResult.setChannel(ErpChannelEnum.ERP_U8);
                    results.add(employeeMappingResult);
                }
            }
        }
        return Result.newSuccess(results);
    }

    private Result<List<EmployeeMappingResult>> queryAllU8EaiEmployee(TimeFilterArg arg, String dataCenterId) {
        List<EmployeeMappingResult> results = Lists.newArrayList();
        //不入库
        Result<StandardListData> StandardListDataRes = erpDataService.listStandardErpObjDataByTime(arg, dataCenterId);
        if (StandardListDataRes.isSuccess()) {
            StandardListData standardListData = StandardListDataRes.getData();
            for (StandardData standardData : standardListData.getDataList()) {
                if (standardData != null && standardData.getMasterFieldVal() != null) {
                    ObjectData objectData = standardData.getMasterFieldVal();
                    if (objectData.get("cpsnmobilephone") == null) {//电话为空的过滤掉
                        continue;
                    }
                    EmployeeMappingResult employeeMappingResult = new EmployeeMappingResult();
                    employeeMappingResult.setErpEmployeePhone(String.valueOf(objectData.get("cpsnmobilephone")));
                    employeeMappingResult.setErpEmployeeName(String.valueOf(objectData.get("name")));
                    employeeMappingResult.setErpEmployeeId(String.valueOf(objectData.get("code")));
                    //employeeMappingResult.setErpEmployeeAccount(String.valueOf(objectData.get("")));
                    employeeMappingResult.setChannel(ErpChannelEnum.ERP_U8_EAI);
                    results.add(employeeMappingResult);
                }
            }
        }
        return Result.newSuccess(results);
    }

    private Result<List<EmployeeMappingResult>> queryAllSapEmployee(TimeFilterArg arg, String dataCenterId) {
        Result<ErpObjectDescResult> object = erpObjectService.queryErpObjectByObjApiName(arg.getTenantId(), dataCenterId, -10000, arg.getObjAPIName());
        if (object == null || !object.isSuccess() || object.getData() == null) {
            return Result.newError(ResultCodeEnum.NOT_SUPPORT_OBJ_EMPLOYEE);
        }
        List<EmployeeMappingResult> results = Lists.newArrayList();
        //不入库
        Result<StandardListData> StandardListDataRes = erpDataService.listStandardErpObjDataByTime(arg, dataCenterId);
        if (StandardListDataRes.isSuccess()) {
            StandardListData standardListData = StandardListDataRes.getData();
            for (StandardData standardData : standardListData.getDataList()) {
                if (standardData != null && standardData.getMasterFieldVal() != null) {
                    ObjectData objectData = standardData.getMasterFieldVal();
                    EmployeeMappingResult employeeMappingResult = new EmployeeMappingResult();
                    employeeMappingResult.setErpEmployeePhone(String.valueOf(objectData.get("")));
                    employeeMappingResult.setErpEmployeeName(String.valueOf(objectData.get("")));
                    employeeMappingResult.setErpEmployeeId(String.valueOf(objectData.get("")));
                    employeeMappingResult.setErpEmployeeAccount(String.valueOf(objectData.get("")));
                    employeeMappingResult.setChannel(ErpChannelEnum.ERP_SAP);
                    results.add(employeeMappingResult);
                }
            }
        }
        return Result.newSuccess(results);
    }

    private Result<List<EmployeeMappingResult>> queryAllK3CloudEmployee(TimeFilterArg arg, String dataCenterId) {
        List<EmployeeMappingResult> results = Lists.newArrayList();
        QueryArg queryArg = new QueryArg();
        queryArg.setFieldKeys("FName,FNumber,FMobile");
        queryArg.setFormId(arg.getObjAPIName());
        queryArg.setLimit(arg.getLimit());
        queryArg.setStartRow(arg.getOffset() * arg.getLimit());
        //筛选非禁用员工
        queryArg.setFilterString("FForbidStatus = 'A'");

        Result<List<K3Model>> result = k3DataManager.queryK3ObjDataALl(arg.getTenantId(), dataCenterId, queryArg);
        if (!result.isSuccess()) {
            log.warn("Query K3Cloud employee error, TenantId={}, result={}", arg.getTenantId(), result);
            return Result.newError(ResultCodeEnum.RESULT_ERROR);
        }
        if (result.getData() != null && result.getData().size() != 0) {
            for (K3Model k3Model : result.getData()) {
                EmployeeMappingResult employeeMappingResult = new EmployeeMappingResult();
                employeeMappingResult.setErpEmployeePhone(String.valueOf(k3Model.get("FMobile")));
                employeeMappingResult.setErpEmployeeName(String.valueOf(k3Model.get("FName")));
                employeeMappingResult.setErpEmployeeId(String.valueOf(k3Model.get("FNumber")));
                employeeMappingResult.setChannel(ErpChannelEnum.ERP_K3CLOUD);
                results.add(employeeMappingResult);
            }
        }
        return Result.newSuccess(results);
    }


    private Result<String> updateEmployeeMapping(String tenantId, EmployeeMappingResult employeeMappingResult) {
        ErpFieldDataMappingEntity erpFieldDataMappingEntity = new ErpFieldDataMappingEntity();
        erpFieldDataMappingEntity.setChannel(employeeMappingResult.getChannel());
        erpFieldDataMappingEntity.setDataType(ErpFieldTypeEnum.employee);
        erpFieldDataMappingEntity.setId(employeeMappingResult.getId());
        erpFieldDataMappingEntity.setFsDataId(employeeMappingResult.getFsEmployeeId() == null ? "" :
                String.valueOf(employeeMappingResult.getFsEmployeeId()));
        erpFieldDataMappingEntity.setFsDataName(employeeMappingResult.getFsEmployeeName() == null ? "" : employeeMappingResult.getFsEmployeeName());
        erpFieldDataMappingEntity.setErpDataId(employeeMappingResult.getErpEmployeeId());
        erpFieldDataMappingEntity.setErpDataName(employeeMappingResult.getErpEmployeeName());
        erpFieldDataMappingEntity.setTenantId(tenantId);
        erpFieldDataMappingEntity.setDataCenterId(employeeMappingResult.getDataCenterId());
        if (StringUtils.isEmpty(employeeMappingResult.getId())) {//插入
            Result<String> ifExist = ifExist(erpFieldDataMappingEntity);
            if (!ifExist.isSuccess()) {
                return ifExist;
            }
            erpFieldDataMappingEntity.setId(com.fxiaoke.api.IdGenerator.get());
            erpFieldDataMappingEntity.setCreateTime(System.currentTimeMillis());
            erpFieldDataMappingEntity.setUpdateTime(System.currentTimeMillis());
            employeeMappingResult.setId(erpFieldDataMappingEntity.getId());
            erpFieldDataMappingEntity.setFieldDataExtendValue(GsonUtil.toJson(employeeMappingResult));
            int insertResult = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(erpFieldDataMappingEntity);
            if (insertResult == 1) {
                Result result = Result.newSuccess();
                result.setData("add");
                return result;
            } else {
                return Result.newError(SYSTEM_ERROR);
            }
        } else {//更新
            Result<String> ifExist = ifExist(erpFieldDataMappingEntity);
            if (!ifExist.isSuccess()) {
                return ifExist;
            }
            erpFieldDataMappingEntity.setUpdateTime(System.currentTimeMillis());
            erpFieldDataMappingEntity.setFieldDataExtendValue(GsonUtil.toJson(employeeMappingResult));
            int updateResult = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(erpFieldDataMappingEntity);
            if (updateResult == 1) {
                Result result = Result.newSuccess();
                result.setData("update");
                return result;
            } else {
                return Result.newError(SYSTEM_ERROR);
            }
        }
    }

    private Result<String> ifExist(ErpFieldDataMappingEntity erpFieldDataMappingEntity) {
        String tenantId = erpFieldDataMappingEntity.getTenantId();
        String dcId = erpFieldDataMappingEntity.getDataCenterId();
        String fsDataId = erpFieldDataMappingEntity.getFsDataId();
        ErpFieldTypeEnum dataType = erpFieldDataMappingEntity.getDataType();
        String erpDataId = erpFieldDataMappingEntity.getErpDataId();
        if (StringUtils.isNotEmpty(fsDataId)) {//如果不为空，校验fs的dataId是否唯一
            List<ErpFieldDataMappingEntity> oldEntry = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listNoSearch(tenantId, dcId, dataType, fsDataId, null);
            if (CollectionUtils.isNotEmpty(oldEntry) && !oldEntry.get(0).getId().equals(erpFieldDataMappingEntity.getId())) {
                Result result = Result.newError(ResultCodeEnum.THE_CRM_EMPLOYEE_MAPPING_EXIST);
                return result;
            }
        }

        if (StringUtils.isNotEmpty(erpDataId)) {//如果不为空，校验erp的dataId是否唯一
            List<ErpFieldDataMappingEntity> oldEntry1 = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listNoSearch(tenantId, dcId, dataType, null, erpDataId);
            if (CollectionUtils.isNotEmpty(oldEntry1) && !oldEntry1.get(0).getId().equals(erpFieldDataMappingEntity.getId())) {
                return Result.newError(ResultCodeEnum.THE_ERP_EMPLOYEE_MAPPING_EXIST);
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> updateEmployeeMappingByErpId(String tenantId, int userId, String dataCenterId, EmployeeMappingResult employeeMappingResult) {
        ErpFieldDataMappingEntity erpFieldDataMappingEntity = new ErpFieldDataMappingEntity();
        erpFieldDataMappingEntity.setTenantId(tenantId);
        erpFieldDataMappingEntity.setChannel(ObjectUtil.defaultIfNull(employeeMappingResult.getChannel(), ErpChannelEnum.STANDARD_CHANNEL));
        String erpEmployeeId = employeeMappingResult.getErpEmployeeId();
        erpFieldDataMappingEntity.setErpDataId(erpEmployeeId);
        erpFieldDataMappingEntity.setDataCenterId(dataCenterId);
        erpFieldDataMappingEntity.setDataType(ErpFieldTypeEnum.employee);
        erpFieldDataMappingEntity.setFsDataId(employeeMappingResult.getFsEmployeeId() == null ? "" :
                String.valueOf(employeeMappingResult.getFsEmployeeId()));
        erpFieldDataMappingEntity.setFsDataName(employeeMappingResult.getFsEmployeeName() == null ? "" : employeeMappingResult.getFsEmployeeName());
        erpFieldDataMappingEntity.setErpDataName(employeeMappingResult.getErpEmployeeName());
        List<ErpFieldDataMappingEntity> oldMapping = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listNoSearch(tenantId, dataCenterId, ErpFieldTypeEnum.employee, null, erpEmployeeId);
        if (CollectionUtils.isNotEmpty(oldMapping) && oldMapping.size() == 1) {//只有一个相同，更新
            erpFieldDataMappingEntity.setId(oldMapping.get(0).getId());
            erpFieldDataMappingEntity.setUpdateTime(System.currentTimeMillis());
            employeeMappingResult.setId(erpFieldDataMappingEntity.getId());
            erpFieldDataMappingEntity.setFieldDataExtendValue(GsonUtil.toJson(employeeMappingResult));
            int updateResult = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(erpFieldDataMappingEntity);
            if (updateResult == 1) {
                Result result = Result.newSuccess();
                result.setData("update");
                return result;
            } else {
                return Result.newError(SYSTEM_ERROR);
            }
        } else {//0个或者多个，删除再插入
            if (CollectionUtils.isNotEmpty(oldMapping)) {//多个
                for (ErpFieldDataMappingEntity entity : oldMapping) {
                    erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteById(entity.getId());
                }
            }
            erpFieldDataMappingEntity.setId(idGenerator.get());
            erpFieldDataMappingEntity.setCreateTime(System.currentTimeMillis());
            erpFieldDataMappingEntity.setUpdateTime(System.currentTimeMillis());
            employeeMappingResult.setId(erpFieldDataMappingEntity.getId());
            erpFieldDataMappingEntity.setFieldDataExtendValue(GsonUtil.toJson(employeeMappingResult));
            int insertResult = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(erpFieldDataMappingEntity);
            if (insertResult == 1) {
                Result result = Result.newSuccess();
                result.setData("add");
                return result;
            } else {
                return Result.newError(SYSTEM_ERROR);
            }
        }
    }

    @Override
    public Result<String> updateOAEmployeeMappingByErpId(String tenantId, EmployeeMappingResult employeeMappingResult) {
        ErpFieldDataMappingEntity erpFieldDataMappingEntity = new ErpFieldDataMappingEntity();
        erpFieldDataMappingEntity.setTenantId(tenantId);
        erpFieldDataMappingEntity.setChannel(employeeMappingResult.getChannel());
        String erpEmployeeId = employeeMappingResult.getErpEmployeeId();
        erpFieldDataMappingEntity.setErpDataId(erpEmployeeId);
        erpFieldDataMappingEntity.setDataType(ErpFieldTypeEnum.employee_oa);
        erpFieldDataMappingEntity.setFsDataId(employeeMappingResult.getFsEmployeeId() == null ? "" :
                String.valueOf(employeeMappingResult.getFsEmployeeId()));
        erpFieldDataMappingEntity.setFsDataName(employeeMappingResult.getFsEmployeeName() == null ? "" : employeeMappingResult.getFsEmployeeName());
        erpFieldDataMappingEntity.setErpDataName(employeeMappingResult.getErpEmployeeName());
        List<ErpFieldDataMappingEntity> oldMapping = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listNoSearch(tenantId, null, ErpFieldTypeEnum.employee_oa, null, erpEmployeeId);
        if (CollectionUtils.isNotEmpty(oldMapping) && oldMapping.size() == 1) {//只有一个相同，更新
            erpFieldDataMappingEntity.setId(oldMapping.get(0).getId());
            erpFieldDataMappingEntity.setUpdateTime(System.currentTimeMillis());
            employeeMappingResult.setId(erpFieldDataMappingEntity.getId());
            int updateResult = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(erpFieldDataMappingEntity);
            if (updateResult == 1) {
                Result result = Result.newSuccess();
                result.setData("update");
                return result;
            } else {
                return Result.newError(SYSTEM_ERROR);
            }
        } else {//0个或者多个，删除再插入
            if (CollectionUtils.isNotEmpty(oldMapping)) {//多个
                for (ErpFieldDataMappingEntity entity : oldMapping) {
                    erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteById(entity.getId());
                }
            }
            erpFieldDataMappingEntity.setId(idGenerator.get());
            erpFieldDataMappingEntity.setCreateTime(System.currentTimeMillis());
            erpFieldDataMappingEntity.setUpdateTime(System.currentTimeMillis());
            employeeMappingResult.setId(erpFieldDataMappingEntity.getId());
            int insertResult = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(erpFieldDataMappingEntity);
            if (insertResult == 1) {
                Result result = Result.newSuccess();
                result.setData("add");
                return result;
            } else {
                return Result.newError(SYSTEM_ERROR);
            }
        }
    }

    @Override
    public Result<String> syncAllUnSyncEmployee(String tenantId, Integer userId, String dataCenterId) {
        Result<List<EmployeeMappingResult>> employees = queryAllUnSyncData(tenantId, userId, dataCenterId);
        return syncErpEmployee(tenantId, userId, dataCenterId, employees.getData());
    }

    @Override
    public Result<String> syncErpEmployee(String tenantId, int userId, String dataCenterId, List<EmployeeMappingResult> employeeMappingResults) {
        Result<List<EmployeeDto>> fsEmployees = fsObjectDataService.getEmployeeFs(Integer.valueOf(tenantId));
        if (fsEmployees == null || !fsEmployees.isSuccess()) {
            Result result = new Result();
            result.setErrCode(fsEmployees.getErrCode());
            result.setErrMsg(fsEmployees.getErrMsg());
            return result;
        }
        Map<String, EmployeeDto> tel2FsEmployeeMap = fsEmployees.getData()
                .stream()
                .filter(employeeDto -> StringUtils.isNotBlank(employeeDto.getMobile()))
                //让normal的在前面
                .sorted(Comparator.comparingInt(v -> v.getStatus().getValue()))
                //如果手机号重复，正常的优先
                .collect(Collectors.toMap(EmployeeDto::getMobile, employee -> employee, (u, v) -> u));
        for (EmployeeMappingResult employeeMappingResult : employeeMappingResults) {
            employeeMappingResult.setDataCenterId(dataCenterId);
            if (StringUtils.isNotEmpty(employeeMappingResult.getErpEmployeePhone())) {//根据电话自动绑定
                EmployeeDto employeeDto = tel2FsEmployeeMap.get(employeeMappingResult.getErpEmployeePhone());
                if (employeeDto != null) {
                    //正常不正常的都能自动绑定
                    employeeMappingResult.setFsEmployeeId(employeeDto.getEmployeeId());
                    employeeMappingResult.setFsEmployeeName(employeeDto.getName());
                    employeeMappingResult.setFsEmployeePhone(employeeDto.getMobile());
                    employeeMappingResult.setFsEmployeeStatus(employeeDto.getStatus().getValue());
                }
            }
            Result<String> updateResult = updateEmployeeMappingByErpId(tenantId, userId, dataCenterId, employeeMappingResult);
            if (!updateResult.isSuccess()) {
                log.info("updateEmployeeMapping failed tenantId={}, userId={}, employeeMappingResult={}", tenantId, userId, employeeMappingResult);
            }
        }
        return Result.newSuccess();
    }

    private String getErpUserId(String erpUserAccount, String tenantId, ErpChannelEnum channel, String dataCenterId) {
        switch (channel) {
            case ERP_U8:
            case ERP_U8_EAI:
            case ERP_DB_PROXY:
                return null;
            case ERP_K3CLOUD:
                return queryK3CloudUserId(tenantId, dataCenterId, erpUserAccount).getData();
            case ERP_SAP:
            case STANDARD_CHANNEL:
                return null;
            default:
                log.info("channel not support: " + channel);
        }
        return null;
    }

    @Override
    public Result<String> queryK3CloudUserId(String tenantId, String dataCenterId, String erpUserAccount) {
        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        K3CloudApiClient k3ApiClient = apiClientHolder.getK3ApiClient(tenantId, connectInfoEntity.getConnectParams(), dataCenterId);
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.SEC_User);
        queryArg.setFieldKeysByList(ImmutableList.of("FUserID", "FName"));
        queryArg.setFilterString(String.format("%s='%s'", "FName", erpUserAccount));
        commonBusinessManager.fillQueryArgByViewExtend(k3ApiClient.getTenantId(), k3ApiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = k3ApiClient.queryReturnMap(queryArg);
        if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            return Result.newSuccess(result.getData().get(0).getString("FUserID"));
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> migrateUserData(String tenantId, ErpChannelEnum channel) {
        log.info("EmployeeMappingServiceImpl.migrateUserData,tenantId={},channel={}", tenantId, channel);

        long startTime = System.currentTimeMillis();
        List<ErpFieldDataMappingEntity> entityList = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getDataListByChannel(tenantId, channel, ErpFieldTypeEnum.employee);
        if (CollectionUtils.isEmpty(entityList)) {
            log.info("EmployeeMappingServiceImpl.migrateUserData,entityList is empty,tenantId={},channel=ERP_K3CLOUD,dataType=employee", tenantId);
            return Result.newSuccess();
        }
        log.info("EmployeeMappingServiceImpl.migrateUserData,entityList.size={}", entityList.size());
        for (ErpFieldDataMappingEntity entity : entityList) {
            try {
                log.info("EmployeeMappingServiceImpl.migrateUserData,entity={}", entity);
                if (StringUtils.isEmpty(entity.getFieldDataExtendValue())) continue;

                EmployeeMappingResult employeeMappingResult = GsonUtil.fromJson(entity.getFieldDataExtendValue(),
                        EmployeeMappingResult.class);
                if (employeeMappingResult == null
                        || StringUtils.isEmpty(employeeMappingResult.getErpUserAccount())
                        || StringUtils.isEmpty(employeeMappingResult.getErpUserId())) {
                    continue;
                }

                ErpFieldDataMappingEntity userEntity = ErpFieldDataMappingEntity.builder()
                        .id(null)
                        .tenantId(entity.getTenantId())
                        .dataCenterId(entity.getDataCenterId())
                        .channel(entity.getChannel())
                        .dataType(ErpFieldTypeEnum.user)
                        .fsDataId(entity.getFsDataId())
                        .fsDataName(entity.getFsDataName())
                        .erpDataId(employeeMappingResult.getErpUserId())
                        .erpDataName(employeeMappingResult.getErpUserAccount())
                        .build();
                Result<String> result = ifExist(userEntity);
                if (!result.isSuccess()) continue;

                userEntity.setId("user_" + idGenerator.get());
                userEntity.setCreateTime(System.currentTimeMillis());
                userEntity.setUpdateTime(System.currentTimeMillis());
                int insert = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .insert(userEntity);
                if (insert != 1) {
                    log.info("EmployeeMappingServiceImpl.migrateUserData,insert failed,entity={}", userEntity);
                }
            } catch (Exception e) {
                log.info("EmployeeMappingServiceImpl.migrateUserData,exception={}", e.getMessage(), e);
            }
        }
        long costTime = System.currentTimeMillis() - startTime;
        log.info("EmployeeMappingServiceImpl.migrateUserData,migrate user data finished,tenantId={},costTime={}", tenantId, costTime);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> migrateUserData4AllTenant(ErpChannelEnum channel) {
        long startTime = System.currentTimeMillis();
        List<String> failedEiList = new ArrayList<>();
        List<String> tenantIdList = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId))
                .getTenantIdListByChannel(channel);
        if (CollectionUtils.isEmpty(tenantIdList)) {
            log.info("EmployeeMappingServiceImpl.migrateUserData4AllTenant,tenantIdList is empty");
            return Result.newSuccess();
        }
        for (int i = 0; i < tenantIdList.size(); i++) {
            log.info("EmployeeMappingServiceImpl.migrateUserData4AllTenant,migrateUserData begin,tenantIdList.size={},i={}", tenantIdList.size(), i);
            String ei = tenantIdList.get(i);
            try {
                migrateUserData(ei, channel);
            } catch (Exception e) {
                log.info("EmployeeMappingServiceImpl.migrateUserData4AllTenant,exception={}", e.getMessage(), e);
                failedEiList.add(ei);
            }
            log.info("EmployeeMappingServiceImpl.migrateUserData4AllTenant,migrateUserData end,tenantIdList.size={},i={}", tenantIdList.size(), i);
        }
        long costTime = System.currentTimeMillis() - startTime;
        log.info("EmployeeMappingServiceImpl.migrateUserData4AllTenant,all tenant user data migrate finished,costTime={},failedEiList={}",
                costTime, failedEiList);
        return Result.newSuccess();
    }

    @Override
    public Result<List<SimpleEmployeeMapping>> listEmployeeMappingByFsUserIds(String tenantId, String dcId, Collection<Integer> userIds) {
        List<String> fsIds = userIds.stream().map(v -> String.valueOf(v)).collect(Collectors.toList());
        List<ErpFieldDataMappingEntity> fieldDataMappingEntities = erpFieldDataMappingDao.listByFsIds(tenantId, dcId, ErpFieldTypeEnum.employee, fsIds);
        List<SimpleEmployeeMapping> resultList = fieldDataMappingEntities.stream()
                .filter(v -> NumberUtil.isInteger(v.getFsDataId()))
                .map(v -> {
                    SimpleEmployeeMapping mapping = new SimpleEmployeeMapping()
                            .setId(v.getId())
                            .setDataCenterId(v.getDataCenterId())
                            .setFsEmployeeId(Integer.valueOf(v.getFsDataId()))
                            .setFsEmployeeName(v.getFsDataName())
                            .setErpEmployeeId(v.getErpDataId())
                            .setErpEmployeeName(v.getErpDataName());
                    return mapping;
                }).collect(Collectors.toList());
        return Result.newSuccess(resultList);
    }

    @Override
    public Result<Void> deleteEmployeeMappingByFsUserId(String tenantId, SimpleEmployeeMapping mapping) {
        //强行设置erpId为null
        mapping.setErpEmployeeId(null);
        if (mapping.getDataCenterId() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL, "datacenterId can not be null");
        }
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, mapping.getDataCenterId());
        List<ErpFieldDataMappingEntity> mappings = erpFieldDataMappingDao.listNoSearch2(tenantId, mapping.getDataCenterId(), ErpFieldTypeEnum.employee, String.valueOf(mapping.getFsEmployeeId()), null);
        log.info("EmployeeMappingServiceImpl.deleteEmployeeMappingByFsUserIds,mappings={}", mappings);
        if (!mappings.isEmpty()) {
            List<String> idList = mappings.stream().map(v -> v.getId()).collect(Collectors.toList());
            erpFieldDataMappingDao.batchDeleteByIds(tenantId, idList);
        }
        //后动作
        try {
            ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(connectInfo.getChannel(), connectInfo.getConnectParams());
            erpDataManager.processChangeEmployeeMapping(mapping, connectInfo);
        } catch (Exception e) {
            log.warn("processChangeEmployeeMapping error", e);
        }
        return Result.newSuccess();
    }
}
