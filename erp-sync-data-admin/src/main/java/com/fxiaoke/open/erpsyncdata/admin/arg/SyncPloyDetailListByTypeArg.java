package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class SyncPloyDetailListByTypeArg implements Serializable {
    /**
     * 1 erp->crm 2 crm->rep
     */
    @ApiModelProperty("1 erp->crm 2 crm->rep")
    private Integer type;
    private String apiName;
    @ApiModelProperty("启用状态筛选 1启用 2停用")
    private Integer status;
    @ApiModelProperty("搜索内容")
    private String searchText;
    @ApiModelProperty("页码")
    private Integer pageNumber;
    @ApiModelProperty("每页大小")
    private Integer pageSize;
}
