package com.fxiaoke.open.erpsyncdata.admin.service.impl.logEvent;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpInterfaceMonitorService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SyncLogNode;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseLinkWalkingService;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogQueryArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpObjInterfaceMonitorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncLogPageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/22 15:09:22
 */
public abstract class AbstractLogService implements BaseLinkWalkingService {
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private BaseLinkServiceImpl baseLinkService;
    @Autowired
    private ErpInterfaceMonitorService erpInterfaceMonitorService;

    private final SyncLogTypeEnum syncLogTypeEnum = this.getClass().getAnnotation(SyncLogNode.class).value();

    protected SyncLogPageArg getSyncLogPageArg(final String tenantId, final String realObjApiName, final List<String> logIds, final StreamLogQueryArg arg) {
        SyncLogPageArg syncLogPageArg = SyncLogPageArg.builder()
                .tenantId(tenantId)
                .type(syncLogTypeEnum)
                .limit(arg.getPageSize())
                .offset((arg.getPageNum() - 1) * arg.getPageSize())
                .logId(logIds)
                .realObjApiName(realObjApiName)
                .excludeFiled(Lists.newArrayList("erpTempData.dataBody"))
                .build();
        if (StringUtils.isNotBlank(arg.getPloyDetailId())) {
            syncLogPageArg.setStreamId(arg.getPloyDetailId());
        }

        if (ObjectUtils.isNotEmpty(arg.getStartTime()) || ObjectUtils.isNotEmpty(arg.getEndTime())) {
            syncLogPageArg.setBeginUpdateTime(new Date(arg.getStartTime()));
            syncLogPageArg.setEndUpdateTime(new Date(arg.getEndTime()));
        }

        if (arg.getStatus() != null) {
            syncLogPageArg.setStatus(arg.getStatus());
        }

        if (ObjectUtils.isNotEmpty(arg.getDataId()) || ObjectUtils.isNotEmpty(arg.getDataNum()) || ObjectUtils.isNotEmpty(arg.getTaskNum())) {
            SyncLogPageArg.QueryTempDataFilterArg tempDataFilterArg = new SyncLogPageArg.QueryTempDataFilterArg();
            tempDataFilterArg.setDataId(arg.getDataId());
            tempDataFilterArg.setDataNum(arg.getDataNum());
            tempDataFilterArg.setTaskNum(arg.getTaskNum());
            syncLogPageArg.setQueryTempDataFilterArg(tempDataFilterArg);
        }
        return syncLogPageArg;
    }

    protected Page<SyncLog> getSyncLogPage(final String tenantId, final String realObjApiName, final List<String> logIds, final StreamLogQueryArg arg) {
        if (StringUtils.isEmpty(realObjApiName)) {
            return new Page<>();
        }
        SyncLogPageArg syncLogPageArg = getSyncLogPageArg(tenantId, realObjApiName, logIds, arg);

        return syncLogManager.pageByFilters(syncLogPageArg);
    }

    protected Result<Page<ErpInterfaceMonitorResult>> getErpInterfaceMonitorPageResult(final String tenantId, String dcId, final Long startTime, final Long endTime, final Integer pageNum, final Integer pageSize, final String realObjApiName, final List<String> objectIds, List<String> readInterface, Integer status, Long queryTime, String lang) {
        QueryErpObjInterfaceMonitorArg queryErpObjInterfaceMonitorArg = new QueryErpObjInterfaceMonitorArg();
        queryErpObjInterfaceMonitorArg.setObjectIds(objectIds);
        queryErpObjInterfaceMonitorArg.setStartTime(startTime);
        queryErpObjInterfaceMonitorArg.setEndTime(endTime);
        queryErpObjInterfaceMonitorArg.setStatus(status);
        queryErpObjInterfaceMonitorArg.setErpObjectApiName(realObjApiName);
        queryErpObjInterfaceMonitorArg.setPageNum(pageNum);
        queryErpObjInterfaceMonitorArg.setPageSize(pageSize);
        queryErpObjInterfaceMonitorArg.setQueryTime(queryTime);
        queryErpObjInterfaceMonitorArg.setInterfaceTypes(readInterface);
        Result<Page<ErpInterfaceMonitorResult>> readResult = erpInterfaceMonitorService.queryObjInterfaceListNotActualAccount(tenantId, dcId, -10000, queryErpObjInterfaceMonitorArg, lang);
        return readResult;
    }


    protected  Result<Page<ErpInterfaceMonitorResult>> queryListByLogIds(final String tenantId, final String realObjApiName, final List<String> logIds, final List<String> readInterface, final StreamLogQueryArg arg, String lang) {
        if (StringUtils.isEmpty(realObjApiName)) {
            return Result.newSuccess(new Page<>());
        }
        SyncLogPageArg syncLogPageArg = getSyncLogPageArg(tenantId, realObjApiName, logIds, arg);
        syncLogPageArg.setExcludeFiled(Lists.newArrayList("erpTempData.dataBody"));
        Page<SyncLog> syncLogs = syncLogManager.pageByFilters(syncLogPageArg);

        Page<ErpInterfaceMonitorResult> resultPage = new Page<>();
        resultPage.setTotalNum(syncLogs.getTotalNum());
        resultPage.setHasNext(syncLogs.isHasNext());
        resultPage.setQueryId(syncLogs.getQueryId());

        if (CollectionUtils.isEmpty(syncLogs.getData())) {
            resultPage.setData(Lists.newArrayList());
            return Result.newSuccess(resultPage);
        }

        // 将logIds转为interfaceIds
        final List<ObjectId> objectIds = syncLogs.getData().stream()
                .map(SyncLog::getData)
                .map(data -> JSON.parseArray(data, String.class))
                .flatMap(List::stream)
                .map(ObjectId::new)
                .collect(Collectors.toList());

        // 调用getErpInterfaceMonitorPageResult
        final Result<List<ErpInterfaceMonitorResult>> listResult = erpInterfaceMonitorService.queryObjInterfaceListByIds(tenantId, objectIds, arg.getPageSize(), lang);
        resultPage.setData(listResult.getData());
        return Result.newSuccess(resultPage);
    }
}
