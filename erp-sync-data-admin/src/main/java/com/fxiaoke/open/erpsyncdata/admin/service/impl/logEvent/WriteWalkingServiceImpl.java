package com.fxiaoke.open.erpsyncdata.admin.service.impl.logEvent;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpInterfaceMonitorService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SyncLogNode;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogQueryArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncLogPageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/6/6 20:39 写节点日志
 * @Version 1.0
 */
@Service("writeWalkingServiceImpl")
@Slf4j
@SyncLogNode(SyncLogTypeEnum.WRITE)
public class WriteWalkingServiceImpl extends AbstractLogService {

    @Autowired
    private ErpInterfaceMonitorService erpInterfaceMonitorService;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private BaseLinkServiceImpl baseLinkService;

    @Override
    public Result<Page<ErpInterfaceMonitorResult>> queryListLogByType(String tenantId, StreamLogQueryArg streamLogQueryArg,String lang) {

        String ployDetailId = streamLogQueryArg.getPloyDetailId();
        String realObjApiName = baseLinkService.getRealApiName(tenantId, ployDetailId,false,lang);
        if (StringUtils.isEmpty(realObjApiName)) {
            return Result.newError(ResultCodeEnum.NO_ERP_OBJECT);
        }
        Page<ErpInterfaceMonitorResult> pageResult = new Page<>();
        List<String> objectIds = null;
        if(StringUtils.isNotEmpty(streamLogQueryArg.getSyncLogId())){
            List<SyncLog> syncLogs = syncLogManager.listByLogId(tenantId, streamLogQueryArg.getSyncLogId(),streamLogQueryArg.getStartTime(), streamLogQueryArg.getEndTime());
            if(CollectionUtils.isEmpty(syncLogs)){
                return Result.newSuccess(pageResult);
            }
            List<SyncLog> filterSyncLog = syncLogs.stream().filter(item -> item.getType().equals(SyncLogTypeEnum.WRITE)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(filterSyncLog)){
                return Result.newSuccess(pageResult);
            }
            objectIds = Lists.newArrayList();
            for (SyncLog syncLog : filterSyncLog) {
                List<String> list = JacksonUtil.fromJson(syncLog.getData(), List.class);
                objectIds.add(list.get(0));
            }
        }
        final List<String> writeInterface = getWriteInterface(tenantId, lang, ployDetailId);

        return getErpInterfaceMonitorPageResult(tenantId, streamLogQueryArg.getDcId(), streamLogQueryArg.getStartTime(), streamLogQueryArg.getEndTime(), streamLogQueryArg.getPageNum(), streamLogQueryArg.getPageSize(), realObjApiName, objectIds, writeInterface, streamLogQueryArg.getStatus(), streamLogQueryArg.getQueryTime(),lang);
    }

    private List<String> getWriteInterface(String tenantId, String lang, String ployDetailId) {
        final SyncPloyDetailResult ployDetail = adminSyncPloyDetailService.getByIdWithCache(tenantId, ployDetailId, lang).getData();
        return ErpObjInterfaceUrlEnum.getWriteInterface(ployDetail.getSourceTenantType());
    }

    @Override
    public Result<?> queryListByLogIds(final String tenantId, final String realObjApiName, final List<String> logIds, final StreamLogQueryArg arg, String lang) {
        final List<String> writeInterface = getWriteInterface(tenantId, lang, arg.getPloyDetailId());
        return queryListByLogIds(tenantId, realObjApiName, logIds, writeInterface, arg, lang);
    }

    //        回写节点特殊逻辑,全链路日志加上回写节点后可删除
    @Override
    protected SyncLogPageArg getSyncLogPageArg(String tenantId, String realObjApiName, List<String> logIds, StreamLogQueryArg arg) {
        final SyncLogPageArg syncLogPageArg = super.getSyncLogPageArg(tenantId, realObjApiName, logIds, arg);
        syncLogPageArg.getTypes().add(SyncLogTypeEnum.REVERSE_WRITE);
        return syncLogPageArg;
    }

    @Override
    public Result<?> queryLogDataById(String tenantId,StreamLogQueryArg.SyncLogDataArg syncLogDataArg,String lang,SyncLog syncLog){
        if(syncLog==null){
            syncLog = syncLogManager.getLogById(tenantId, new ObjectId(syncLogDataArg.getId()), syncLogDataArg.getNodeEnum());
        }
        List<ObjectId> objectIdList = new ArrayList<>();
        if(ObjectUtils.isNotEmpty(syncLog)){
            List<String> list = JSONObject.parseObject(syncLog.getData(), List.class);
            objectIdList.add(new ObjectId(list.get(0)));
        }
        Result<List<ErpInterfaceMonitorResult>> afterResult = erpInterfaceMonitorService.getObjInterfaceMonitorByObjIds(tenantId, objectIdList,lang);
        if(CollectionUtils.isNotEmpty(afterResult.getData())){
            for (ErpInterfaceMonitorResult datum : afterResult.getData()) {
                datum.setSyncLogId(syncLog.getLogId());
            }
            return Result.newSuccess(afterResult.getData().get(0));
        }
        return Result.newSuccess();
    }

}
