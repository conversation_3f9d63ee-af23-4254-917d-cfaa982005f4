package com.fxiaoke.open.erpsyncdata.admin.remote

import com.fxiaoke.open.erpsyncdata.dbproxy.remote.base.result.EgressResult
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.base.service.EgressApiService
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.result.GeoAddressResult
import spock.lang.Specification
/**
 * <AUTHOR> 
 * @date 2023/11/1 19:43:33
 */
//@ContextConfiguration("classpath:test-Geo.xml")
class EgressApiManagerTest extends Specification {
//    static {
//        System.setProperty("process.profile", "fstest");
//        System.setProperty("process.name", "fs-erp-sync-data-web");
//    }

//    @Autowired
    private EgressApiManager egressApiManager

    def "getDistrict - #name"() {
        given:
        def service = Mock(EgressApiService) {
            getGeoAddress(*_) >> { args ->
                def key = args[0] as String
                def res = new GeoAddressResult(province: key)
                return new EgressResult(data: res, code: resCode)
            }
        }
        egressApiManager = new EgressApiManager(egressApiService: service)
        expect:
        println egressApiManager.getDistrict("广东", "province")
//        println egressApiManager.getDistrict("深圳", "city")
//        println egressApiManager.getDistrict("深圳大冲国际大厦", "district")
        where:
        name        | resCode
        "获取成功"    | EgressResult.SUCCESS_CODE
        "获取失败"    | 0
    }
}
