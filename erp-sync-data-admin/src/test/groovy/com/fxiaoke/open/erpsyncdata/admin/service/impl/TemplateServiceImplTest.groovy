package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.fxiaoke.open.erpsyncdata.admin.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.TemplateDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.TemplateOptionDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TemplateDoc
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TemplateOptionDoc
import org.bson.types.ObjectId

import java.util.function.Supplier

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/4/10
 */
class TemplateServiceImplTest extends BaseSpockTest {

    private TemplateServiceImpl templateService
    private TemplateDao templateDao
    private TemplateOptionDao templateOptionDao
    Supplier templateSup
    Supplier optionSup

    def emptyData() {
        templateDao = Mock(TemplateDao)
        //模拟原来数据为空
        templateDao.listAllBaseInfo() >> {
            return []
        }
        templateOptionDao = Mock(TemplateOptionDao)
        templateService = new TemplateServiceImpl(
                templateDao: templateDao,
                templateOptionDao: templateOptionDao
        )
    }

    def "测试加载文件-空文件空数据"() {
        when:
        emptyData()
        Supplier templateSup = { -> [] }
        Supplier optionSup = { -> [] }
        templateService.loadTemplateFromSup(templateSup, optionSup)
        then:
        0 * templateOptionDao.upsertIgnore(_)
        0 * templateDao.replaceById(_)
    }

    def buildTemplateDoc(int num) {
        def docs = []
        for (final def i in 1..num) {
            docs.add(new TemplateDoc(id: new ObjectId()))
        }
        return docs
    }


    def buildTemplateOptionDoc(int num) {
        def docs = []
        for (final def i in 1..num) {
            docs.add(new TemplateOptionDoc(id: new ObjectId()))
        }
        return docs
    }


    def "测试加载文件-空数据多文件"() {
        emptyData()
        Supplier templateSup = { -> buildTemplateDoc(10) }
        Supplier optionSup = { -> buildTemplateOptionDoc(20) }
        when:
        templateService.loadTemplateFromSup(templateSup, optionSup)
        println("finish")
        then:
        10 * templateDao.replaceById(_)
        20 * templateOptionDao.upsertIgnore(_)
    }


    def "测试加载文件-文件版本低"() {
        versionSet("1.32", "1.0")
        when:
        templateService.loadTemplateFromSup(templateSup, optionSup)
        println("finish")
        then:
        0 * templateDao.replaceById(_)
        0 * templateOptionDao.upsertIgnore(_)
    }


    def "测试加载文件-db版本一样"() {
        versionSet("2.0", "2.0")
        when:
        templateService.loadTemplateFromSup(templateSup, optionSup)
        println("finish")
        then:
        0 * templateDao.replaceById(_)
        0 * templateOptionDao.upsertIgnore(_)
    }


    def "测试加载文件-db版本低"() {
        versionSet("2.0", "2.122")
        when:
        templateService.loadTemplateFromSup(templateSup, optionSup)
        println("finish")
        then:
        1 * templateDao.replaceById(_)
        1 * templateDao.disableTemplates(*_)
        20 * templateOptionDao.upsertIgnore(_)
    }


    def "测试加载文件-新增、更新停用集合"() {
        given:
        templateDao = Mock(TemplateDao)

        def id1 = new ObjectId()
        def id2 = new ObjectId()
        def id3 = new ObjectId()
        def id4 = new ObjectId()
        def dbDoc1 = new TemplateDoc(id: id1, title: "t1", version: "1.0")
        def dbDoc2 = new TemplateDoc(id: id2, title: "t2", version: "1.11")
        def dbDoc3 = new TemplateDoc(id: id3, title: "t3", version: "1.21")
        def fileDoc1 = new TemplateDoc(id: id1, title: "t1", version: "2.0")
        def fileDoc2 = new TemplateDoc(id: id2, title: "t2-update", version: "2.0")
        def fileDoc4 = new TemplateDoc(id: id4, title: "t4-add", version: "2.0")
        templateDao.listAllBaseInfo() >> {
            return [dbDoc1, dbDoc2, dbDoc3]
        }
        templateSup = { -> [fileDoc1, fileDoc2, fileDoc4] }
        templateOptionDao = Mock(TemplateOptionDao)
        templateService = new TemplateServiceImpl(
                templateDao: templateDao,
                templateOptionDao: templateOptionDao
        )
        optionSup = { -> buildTemplateOptionDoc(20) }
        when:
        templateService.loadTemplateFromSup(templateSup, optionSup)
        println("finish")
        then:
        //id1,2更新,
        1 * templateDao.upsertIgnore(fileDoc1, dbDoc1.getVersion())
        1 * templateDao.upsertIgnore(fileDoc2, dbDoc2.getVersion())
        //3 停用
        1 * templateDao.disableTemplates([id3])
        //4插入
        1 * templateDao.replaceById(fileDoc4)
        20 * templateOptionDao.(_)
    }

    private void versionSet(String dbVersion, String fileVersion) {
        templateDao = Mock(TemplateDao)
        def dbDoc = new TemplateDoc(id: new ObjectId(), title: "dbTitle", version: dbVersion)
        def fileDoc = new TemplateDoc(id: new ObjectId(), title: "fileTitle", version: fileVersion)
        templateDao.listAllBaseInfo() >> {
            return [dbDoc]
        }
        templateOptionDao = Mock(TemplateOptionDao)
        templateService = new TemplateServiceImpl(
                templateDao: templateDao,
                templateOptionDao: templateOptionDao
        )
        templateSup = { -> [fileDoc] }
        optionSup = { -> buildTemplateOptionDoc(20) }
    }
}
